
$(document).ready(function() {


$('#contact_form .from-button').click(function () {
    $.ajax({
        type: 'post',
        url: 'mail.php',
        data: $('#contact_form').serialize(),
        success: function () {
          $('#contact_form')[0].reset();
          $('#contact_form .from-button').attr('style', "background-color:#16C39A");
          $('#contact_form .jk').html("<i style='color:#16C39A'>*</i> Email has been sent successfully");
        }
    });
    return false;
});

var headerId = $(".sticky-header");
  var headerTop = $(".sticky-header .header_top_area");

  $(window).on('scroll', function () {
    var amountScrolled = $(window).scrollTop();
    if ($(this).scrollTop() > 50) {
      headerId.removeClass("not-stuck");
      headerId.addClass("stuck");
      headerTop.addClass("display-none");
    } else {
      headerId.removeClass("stuck");
      headerId.addClass("not-stuck");
      headerTop.removeClass("display-none");
    }
  });
 
// preloader - start

$(window).on('load', function () {
  $('#preloader').fadeOut('slow', function () { $(this).remove(); });
});
setTimeout(function()
{ $('#preloader').addClass('d-none'); }, 3000);

// preloader - end


// mobile menu toggle


// navbar //

  if ($(window).width() > 991){
    $('.navbar-light .d-menu').hover(function () {
            $(this).find('.sm-menu').first().stop(true, true).slideDown(150);
        }, function () {
            $(this).find('.sm-menu').first().stop(true, true).delay(120).slideUp(100);
        });
        }

          window.addEventListener('scroll', function() {
              if (window.scrollY > 50) {
                document.getElementById('navbar_top').classList.add('fixed-top');
                // add padding top to show content behind navbar
                navbar_height = document.querySelector('.navbar').offsetHeight;
                document.body.style.paddingTop = navbar_height + 'px';
              } else {
                document.getElementById('navbar_top').classList.remove('fixed-top');
                 // remove padding top from body
                document.body.style.paddingTop = '0';
              } 
          });

  // end-nabvar

// count js - start


$(document).ready(function() {

  var counters = $(".count");
  var countersQuantity = counters.length;
  var counter = [];

  for (i = 0; i < countersQuantity; i++) {
    counter[i] = parseInt(counters[i].innerHTML);
  }

  var count = function(start, value, id) {
    var localStart = start;
    setInterval(function() {
      if (localStart < value) {
        localStart++;
        counters[id].innerHTML = localStart;
      }
    }, 40);
  }

  for (j = 0; j < countersQuantity; j++) {
    count(0, counter[j], j);
  }


//team Selection
$('.client').owlCarousel({
  loop:true,
  slideToshow:3,
  slideToscroll:3,
  speed:30,
  margin:10,
  nav:true,
  autoplay:true,
  responsive:{
      0:{
          items:1
      },
      600:{
          items:3
      },
      1000:{
          items:5
      }
  }
})
});

//testimonial slider

$('.testimonials').not('.slick-initialized').slick({
  slidesToShow: 3,
  slidesToScroll: 1,
  speed: 300,
  dots:true,
  centerMode: true,
  centerPadding: '0px',
  autoplay: true,
  autoplaySpeed: 2000,
  responsive: [
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        dots: true,
        arrows:false
      }
    }
    
  ]
});


//-----------------------------------------------
 // top-to-back - start 
 // ----------------------------------------------
if ($(window).scrollTop() < 100) {
    $('.scrollToTop').hide();
  }
  
  $(window).scroll(function() {
    if ($(this).scrollTop() > 100) {
      $('.scrollToTop').fadeIn('slow');
    } else {
      $('.scrollToTop').fadeOut('slow');
    }
  });
  $('.scrollToTop').click(function(){
    $('html, body').stop().animate({scrollTop:0}, 500, 'swing');
    return false;
  });
  
// -----------------------------------------------
 // top-to-back - start 
 // ----------------------------------------------

// This is wow js

  new WOW().init();


//gallery//
$(document).ready(function(){

$(".filter-button").click(function(){
        var value = $(this).attr('data-filter');

        if(value == "all")
        {
        
        $('.filter').show('1000');
        }
        else
        {
        
        $(".filter").not('.'+value).hide('3000');
        $('.filter').filter('.'+value).show('3000');
        
        }
        });

        if ($(".filter-button").removeClass("active")) {
        $(this).removeClass("active");
        }
        $(this).addClass("active");


        });

        // 
});