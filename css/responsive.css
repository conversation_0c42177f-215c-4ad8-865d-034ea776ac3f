@media screen and (max-width: 1799.89px){
  .breadcrumb-menu {
    color: #ffffff;
  }

}

@media screen and (max-width: 1680px){
   .slider-content{
    transform: translateY(-230%);
   }
}
@media screen and (max-width: 1585px){
  .slider-content {
    transform: translateY(-250%);
    -webkit-transform: translateY(-250%);
    -moz-transform: translateY(-250%);
    -ms-transform: translateY(-250%);
    -o-transform: translateY(-250%);
  }
}

  @media screen and (max-width: 1499px){
    .slider-content {
      transform: translateY(-250%)  ;
      -webkit-transform: translateY(-250%)  ;
      -moz-transform: translateY(-250%)  ;
      -ms-transform: translateY(-250%)  ;
      -o-transform: translateY(-250%)  ;
    }
  

}

@media screen and (max-width: 1399px){
  .slider-content {
    transform: translateY(-220%);
    -webkit-transform: translateY(-220%);
    -moz-transform: translateY(-220%);
    -ms-transform: translateY(-220%);
    -o-transform: translateY(-220%);
  }
}

@media screen and (max-width: 1232.99px){
  .client .owl-nav .owl-prev{
    left: -4%;
  }
  .client .owl-nav .owl-next{
    right: -4%;
  }
}
@media screen and (max-width: 1299.98px){
  .section-tittle .bottom-style::before{
    right: 44%;
  }
  .section-tittle .bottom-style::after{
    left: 44%;
  }
}

@media screen and (max-width: 1199.98px){
  .navbar-collapse .navbar-nav .nav-item{
    margin-right: 1rem !important;
  }
  .navbar-nav .contact-btn{
    margin-left: 0px !important;
  }
  .navbar-nav .nav-item .nav-link{
    padding: 10px;
  }
  .navbar-collapse button{
    margin-top: 20px;
  }
  .feature-details{
    height: 260px;
  }
  .client-details h5::before{
    left: -5px;
  }
  .carousel-item img{
    height: auto;
  }
  .overlay{
    width: 100%;
    height: 100%;
    top: 4%;
    left: 15%;
  }
  p{
    margin-bottom: 30px;
  }

  .section-tittle p{
    margin-bottom: 40px;
  }
  .blog_content{
    padding: 6px 15px;
  }

  .blog_content h6{
    margin-top: 10px;
  }
  .contact_img{
    width: 100%;
  }
  .about_para q{
    display: none;
  }
  .others-ofice{
    padding: 0px;
  }
  .detsils-breadcrumb {
    left: 22%;
  }
}

@media screen and (max-width: 1091.98px){
  .slider-content {
    transform: translateY(-190%) ;
    -webkit-transform: translateY(-190%) ;
    -moz-transform: translateY(-190%) ;
    -ms-transform: translateY(-190%) ;
    -o-transform: translateY(-190%) ;
}
  #vedio-demo {
    height: 60%;
  }
  .main-promo {
    top: 60%;
  }
  .testimonial-area {
    min-height: 50vh;
  }

}

@media screen and (max-width: 1024.98px){
  .detsils-breadcrumb {
    left: 18%;
  }
}

@media screen and (max-width: 991.98px){
  #navbarSupportedContent ul li a{
    padding: 12px 30px;
  }
  .hori-selector{
    margin-top: 0px;
    margin-left: 10px;
    border-radius: 0;
    border-top-left-radius: 25px;
    border-bottom-left-radius: 25px;
  }
  .hori-selector .left,
  .hori-selector .right{
    right: 10px;
  }
  .hori-selector .left{
    top: -25px;
    left: auto;
  }
  .hori-selector .right{
    bottom: -25px;
  }
  .hori-selector .left:before{
    left: -25px;
    top: -25px;
  }
  .hori-selector .right:before{
    bottom: -25px;
    left: -25px;
  }
  .slider-content {
    transform: translateY(-150%);
    -webkit-transform: translateY(-150%);
    -moz-transform: translateY(-150%);
    -ms-transform: translateY(-150%);
    -o-transform: translateY(-150%);
}
  .language-select{
    position: absolute;
    margin-left: 40px;
  }
  .top-info {
    display: none !important;
  }
  .navbar{
    width: 100% !important;
  }
  .navbar-collapse{
    overflow-y: scroll;
    max-height: 300px;
  }
  .navbar-links li.navbar-dropdown:hover .dropdown {
    transform: translateX(90px) !important;
    -webkit-transform: translateX(90px) !important;
    -moz-transform: translateX(90px) !important;
    -ms-transform: translateX(90px) !important;
    -o-transform: translateX(90px) !important;
  }
  .navbar-links li.navbar-dropdown .dropdown {
  top: 35% !important;
  }
  .navbar-links {
    display: inline-table !important;
  }
  .navbar-nav{
    display: none      ;
  }
  .header_top_area{
    display: none;
  }
  .section-tittle .bottom-style::before{
    right: 42%;
  }
  .section-tittle .bottom-style::after{
    left: 42%;
  }
  .header-top{
    display: none;
  }
  .menu-sticky{
    padding: 0px 0px; 
  }
  .navbar-collapse .menu-btn .menu-contac-button{
    padding: 15px 30px;
  }
  .navbar-collapse .menu-btn{
    padding-bottom: 30px;
  }
  .dropdown-menu{
    border-left: 1px solid #ddd !important;
  }
  .topbar-socail{
    justify-content: center !important;
  }
  .top-bar-list{
    display: block !important;
  }
  .top-bar-list ul{
    justify-content: center;
  }
  .topbar-ul1:after{
    display: none;
  }
  .topbar-ul2:after{
    display: none;
  }
  .navbar-toggler{
    border: 1px solid #ddd;
  }
  .navbar-toggler{
    padding: .25rem .45rem;
    outline: none !important;
  }
  .navbar-toggler-icon{
    width: 40px; 
    height: 1px; 
    display: flex; 
    padding: 1px 0px;
    background: #ffffff;
    margin: 6px 0px 4px 0px;
    background: #ccc;
  }
  .navbar-nav .nav-item.active .nav-link:before {
    display: none;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu{
    border: none;
  }
  .navbar .navbar-collapse .navbar-nav li a:before{
    border-bottom: none;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu a{
    border-bottom: none !important;
  }
  .navbar .navbar-collapse .navbar-nav li a:hover:before{
    visibility: hidden;
  }
  .navbar-nav {
    margin-bottom: 40px;
  }
  .carousel-indicators {
    position: absolute;
    bottom: 5%;
    z-index: 9;
    right: 0%;
    transform: rotate(180deg);
  }
  .slider-content{
    transform: translateY(-125%) ;
    -webkit-transform: translateY(-125%) ;
    -moz-transform: translateY(-125%) ;
    -ms-transform: translateY(-125%) ;
    -o-transform: translateY(-125%) ;
}
  .slider-content h2{
    font-size: 40px;
  }
  .testimonial-box {
    padding: 0px 0px 30px 0px; 
  }
  .client .owl-nav .owl-prev{
    top: 50px;
  }
  .client .owl-nav .owl-next{
    top: 50px;
  }
  .corpo-about-us{
    margin-top: 50px !important;
  }
  .wrapper {
    height: 100vh;
  }
  .contact-us {
    margin-top: 14px;
  }
  .left{
    margin-bottom: 30px;
  }

}
@media(min-width: 992px){
  .navbar-expand-custom {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-custom .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-custom .navbar-toggler {
    display: none;
  }
  .navbar-expand-custom .navbar-collapse {
    display: -ms-flexbox!important;
    display: flex!important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }

}
@media only screen and (max-width:990px) {
  .box1 {
    margin-bottom: 30px
  }
}


@media screen and (max-width: 800px){
  .slider-content {
    width: 120%;
  }
  .slider-content {
    transform: translateY(-150%);
    -webkit-transform: translateY(-150%);
    -moz-transform: translateY(-150%);
    -ms-transform: translateY(-150%);
    -o-transform: translateY(-150%);
}
  #vedio-demo {
    height: 60%;
  }
  #corpo-business-pricing{
    padding: 20px 0px;
  }
  .testimonial-area {
    min-height: 64vh;
  }
  .right {
    height: auto;
  }
  .detsils-breadcrumb {
    left: 23%;
  }
}


@media screen and (max-width: 768.98px){
  .language-select {
    position: absolute;
    margin-left: -75px;
    margin-top: 6px;
  }
  .navbar-brand{
    width: 66%;
  }
  .slider-content {
    line-height: 2rem ;
    transform: translateY(-175%) ;
    -webkit-transform: translateY(-175%) ;
    -moz-transform: translateY(-175%) ;
    -ms-transform: translateY(-175%) ;
    -o-transform: translateY(-175%) ;
  }
  .slider-content h1{
    margin-bottom: 40px;
    line-height: 1.2;
  }
  .navbar-brand img{
    width: 35% !important;
  }
  #corpo-about-us{
    margin-top: 10%;
  }
  .about-btn {
    margin-top: 0px !important;
  }
  .blockquote{
    display: none;
  }
  .top-info .personal-info li {
    padding: 1px 10px !important;
  }
  .navbar-collapse .menu-btn .menu-contac-button{
    padding: 15px 30px !important;
  }
  .breadcrumb-menu h3{
    font-size: 26px;
    margin-bottom: 5px;
  }
  .breadcrumb li a{
    font-size: 12px;
  }
  .breadcrumb .active {
    font-size: 12px; 
  }
   .reply-btn{
    right: 20px;
  }
  #vedio-demo {
    height: 60%;
  }
  .vedio-title p {
    padding: 0px 0px;
  }
  .portfolio-contant{
    height: 30%;
  } 
  .portfolio-details a img{
    width: 100%;
  }

  .banner_image img{
    margin-top: 40px;
  }
  .vedio_image i{
    padding:25px ;
  }
  #Our-mission-section {
    margin-top:0px;
  }

  .overlay {
    left: 3%;
    top: 4%;
    width: 94%;
    height: 92%;
  }
  .vedio_shape-2 {
    bottom: -10%;
    left: 66%;
  }
  .Course-details a {
    margin-right: 72%;
  }
  .Course-details .span_item {
    margin-left: 55%;
  }
  .aboutus-banner{
  margin-top: 40px;
  }
  .blog-img img{
  width: 100%;
  }
  .value_image img{
  margin-top: 40px;
  }
  .Course-details a {
    margin-right: 70%;
  }
  .value_image {
    margin-top: 10px;
  }

  .two > li{
    margin: 0px 1px;
  }
  .two {
      margin: 20px 0px;
  }
  .blog_content {
    left: 5%;
  }
  .blog3{
    margin: 0;
  }
  .Course-details a{
    margin-right: 54%;

  }
  .Course-details .span_item {
    margin-left: 29%;
  }
  .span_item p {
    margin-bottom: 30px;
  }
  .detsils-breadcrumb {
    left: 18%;
  }
  .contact-btn{
    margin-top: 20px;
  }
  .slider-content p{
    display: none;
  }

}



@media screen and (max-width: 755px){
  .blog-card{
  margin-bottom: 70px;
  }

}


@media screen and (max-width: 640px){
  .slider-content {
  line-height: 1rem ;
  transform: translateY(-215%) ;
  -webkit-transform: translateY(-215%) ;
  -moz-transform: translateY(-215%) ;
  -ms-transform: translateY(-215%) ;
  -o-transform: translateY(-215%) ;
  width: 80%;
}
  #vedio-demo {
    height: 60%;
  }
  .back-cta .back-sec-btn a {
    padding: 16px 9px 19px;
  }
  .detsils-breadcrumb {
    left: 24%;
  }

}

@media screen and (max-width: 601px){
  .main-promo {
    top: 62%;
  }
  .about-text p {
    margin-bottom: 0px;
  }
  .vedio-title p {
    padding: 0px 0px;
  }
  #vedio-demo {
    height: 60%;
    width: auto;
  }
  .download-btn-wrap a{
    margin-right: 15px;
    
  }
  #brand-section {
    padding: 10px 0px;
  }
  #corpo-business-pricing{
    padding: 20px 0px;
  }
  .feature-content {
    margin-bottom: 20px;
  }
  .copyright-text p{
    margin-bottom: 0px !important ;
  }
  .slider-content {
    line-height: 2rem ;
    transform: translateY(-170%) ;
    -webkit-transform: translateY(-170%) ;
    -moz-transform: translateY(-170%) ;
    -ms-transform: translateY(-170%) ;
    -o-transform: translateY(-170%) ;
    width: 80%;
}
  .testimonial-area {
    min-height: 60vh;
  }
  .courses_details-2 img{
    width: 100%;
  }
  .detsils-breadcrumb {
    left: 24%;
  }
}
@media screen and (max-width: 575.98px){

  .slider-content h2{
    font-size: 36px;
  }
  .home-1-panel-title button{
    font-size: 16px;
  }
  .client .owl-item img{
    width: 60%;
  }
  .carousel-item img{
    height: auto;
  }
  section{
    padding: 40px 0px;
  }
  .vedio-title h2 {
    margin-top: 30px;
  }
  #vedio-demo{
    height: 80%;
  }
  .corpo-working-process{
    margin-top: 40px;
  }

}

@media screen and (max-width: 540px){
  .detsils-breadcrumb{
    left: 23%;
  }
  .slider-content h1 {
    margin-bottom: 30px;
    color: #fff !important;
    font-size: 28px;
  }
  .common-title h2 {
    font-size: 25px;
  }
  .about-text h5 {
    font-size: 1.3rem;
  }
}
@media screen and (max-width: 481px){
  .portfolio-contant {
    height: 40%;
  }

  footer .scrollToTop{
    right: 5px;
  }
  .slider-content {
    width: 98%;
  }
  .slider-content h1 {
    margin-bottom: 30px;
    line-height: 1.9rem;
  }
  .hidden1st{
    display: none;
  }
  #corpo-promo{
    display: none !important;
  }
  #corpo-about-us {
    margin-top: 0%;
  }
  #vedio-demo {
    height: 76%;
  }
  section {
    padding: 30px 0px !important;
  }
  p {
    margin-bottom: 30px;
  }
  .vedio-title p {
    padding: 0px 0px;
  }
  .detsils-breadcrumb {
    left: 26%;
  }
}

@media screen and (max-width: 466.98px){
  .event1, .event2, .event3, .event4 {
    padding: 0px 15px;
  }
}

@media screen and (max-width: 414.98px){
  
  .slider-content {
    transform: translateY(-150%) !important;
    -webkit-transform: translateY(-150%) !important;
    -moz-transform: translateY(-150%) !important;
    -ms-transform: translateY(-150%) !important;
    -o-transform: translateY(-150%) !important;
  }
  .slider-content h1 {
    margin-bottom: 10px;
    color: #fff !important;
    font-size: 23px;
    line-height: 1.7rem;
  }
  .slider-content {
    width: 90%;
  }
  .slider-content a {
    padding: 10px 20px;
  }
  #vedio-demo {
    height: 80%;
  }
  .footer-cta {
    padding-bottom: 0rem !important;
  }
  .detsils-breadcrumb {
    left: 30%;
  }
  .sub-title h1{
    font-size: 32px;
  }
  .sub-title p{
    font-size: 12px;
    letter-spacing: 1px;
  }
  .right_heading h4 {
    line-height: 32px !important;
  }
  .slider-content p{
    display: none;
  }
  .slider-content h1{
    margin-bottom: 30px;
  }
  
}
	
@media screen and (max-width: 386px){

  .blog_content{
    left: 15%;
    width: 70%;
  }
  .slider-content p {
    margin-bottom: 10px;

  }
  .slider-content a {
    padding: 7px 13px;
  }
  .feature-content {
    margin-bottom: 0px !important;
  }
  #vedio-demo {
    height: 80%;
  }
  .back-sec-btn{
    float: left;
    margin-top: 20px;
  }
  .label {
    left: -138px;
  }
  .detsils-breadcrumb{
    left: 32%;
  }
  .about-content ul{
    display: block;
  }
  .abt-li1,
  .abt-li2{
    margin-bottom: 20px;
  }

}

@media screen and (max-width: 375px){
  section {
    padding: 0px;
  }

  .aboutus-banner{
    margin-top: 30px;
  }
  .section-tittle{
    margin-top: 40px;
  }

  .banner_image img {
      margin-top: 35px;
  }

  .portfolio-contant {
      height: 50%;
  }
  .footer-service h3{
    margin-top: 20px;
  }
  .section-tittle small {
      top: 0px;
  }
  section{
    padding: 0px 0px;
  }

  .footer-about{
    margin-top: 40px;
  }

  .btn{
    font-size: 15px;
    padding: 7px;
  }

  .overlay {
    left: 6.5%;
    top: 9%;
    width: 87%;
    height: 82%;
  }
  .blog1 {
      margin-bottom: 25px !important;
  }
  .contact_img{
    width: 100%;
  }
  .slider-content a {
    padding: 8px 16px;
  }
  .slider-content p{
    margin-bottom: 10px;
  }
  #vedio-demo {
    height: 80%;
  }
  .sub-title p {
    font-size: 11px;
    letter-spacing: 0px;
  }
  .detsils-breadcrumb {
    left: 32%;
  }

}
@media screen and (max-width: 360px){
  #vedio-demo {
    height: 85%;
  }
  .para {
    padding-right: 50px;
  }
  .sub-title h1{
    font-size: 22px;
  }
  .sub-title p{
    font-size: 12px;
  }
  .label {
    position: static;
    margin-bottom: 44px;
  }
  label {
    margin-bottom: 22px;
  }
  #page-breadcrumb {
    height: 200px;
  }
  #page-breadcrumb .container{
    padding-left: 20px;
  }
  .subscribe-form input {
    padding: 21px 28px;
  }
  .detsils-breadcrumb{
    left: 33%;
  }
}


@media screen and (max-width: 320px){

  .breadcrumb-menu h3 {
    font-size: 22px;
    margin-bottom: 5px;
  }
  section{
    padding: 40px 0px;
  }
  #vedio-demo {
    height: 85%;
  }
  .vedio-title h2 {
    margin-top: 0px;
    font-size: 1.2rem;
    line-height: 1.2;
  }
  .navbar-brand{
    width: 70%;
  }
  .slider-content h1 {
    font-size: 22px;
  }
  .slider-content h1 {
    margin-bottom: 20px;
  }
  .slider-content {
    transform: translateY(-130%) !important;
    -webkit-transform: translateY(-130%) !important;
    -moz-transform: translateY(-130%) !important;
    -ms-transform: translateY(-130%) !important;
    -o-transform: translateY(-130%) !important;
    width: 100%;
  }
  .common-title h2 {
    font-size: 1.2rem;
  }
  .about-text h5 {
    font-size: 1rem;
  }

}



@media screen and (max-width: 281px){
  .blog-date {
    top: 0%;
    right: 6%;
  }
  .feature-content {
    padding: 30px;
  }
  body{
    overflow: visible;
  }
  .vedio-title h2 {
    color: #fff;
    font-size: 30px;
    line-height: 2.5rem;
  }
  .btn {
    font-size: 15px;
    padding: 7px;
  }
  .vedio-title{
    margin-bottom: 5px !important;
  }
  .others-icon {
    display: none;
  }
  .left {
    right: -15px;
  }
  .detsils-breadcrumb {
    left: 38%;
  }
  .slider-content p {
    display: none;
  }
  .slider-content h1 {
    line-height: 1.5;
  }
  .slider-content{
    width: 90%;
    transform: translateY(-130%) !important;
    -webkit-transform: translateY(-130%) !important;
    -moz-transform: translateY(-130%) !important;
    -ms-transform: translateY(-130%) !important;
    -o-transform: translateY(-130%) !important;
  }
  .about-text .about-content ul li {
    text-align: center;
    padding-inline: 10px;
  }
}
@media screen and (max-width: 240px){
  .navbar-brand{
    width: 70%;
  }
  .navbar-brand {
    width: 65%;
  }
  .detsils-breadcrumb {
    left: 38%;
  }
}
/*New responsive css*/