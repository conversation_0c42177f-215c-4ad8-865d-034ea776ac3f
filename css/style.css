@import url('https://fonts.googleapis.com/css2?family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,500;1,600;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300;0,600;0,900;1,300;1,500;1,800&display=swap');

/*=========table of content start

  Template Name: Cropking
  Template URI: https://theme.bitspecksolutions.com/html-template/cropking

==========================

CSS 

==========================

preloader

===============================

1. Header section
2. Hero section
5. Promo section
3. About section
6. Features section
7. Counter section
8. Portfolio section
9. Team section
10. video section
11. Pricing section
12. Brand section
13. Blog section
14. Footer section
15. Other pages


=================================

Index Page

==================================

==============table of content end*/

/*====================================
 global area - start
====================================*/

*{
  padding: 0;
  margin: 0;  
  outline: 0px;
  scroll-behavior: smooth;
}

html {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible;
}

body {
  /* font-family:'Raleway' sans-srif; */
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5rem;
  width: 100%;
  overflow-x: hidden;
  background-color: #fff;
}

html, body, .wrapper {
  height: 100%;
  width: 100%;
}

section{
  padding: 80px 0px;
}

h1, h2, h3, h4, h5, h6 {
  color: #0eafec;
  font-weight: bold;
  line-height: 2.5rem;
  /* font-family: 'Poppins' Rubik; */
  letter-spacing: 0;
  margin-bottom: 15px;
}

img {
  border: none;
  outline: none;
  max-width: 100%;
}

label {
  display: inline-block;
  font-weight: normal;
  margin-bottom: 5px;
  max-width: 100%;
}

a img,
button,
iframe {
  border: none;
  text-decoration: none !important;
}

p {
  font-family: 'Rubik';
  color: #232323;
  line-height: 1.5rem;
  text-transform: none;
  font-weight:200;
  margin-bottom: 50px;
}

input:focus, textarea:focus, 
select:focus {
  outline: none;
  box-shadow: inherit;
  -webkit-box-shadow: inherit;
  -moz-box-shadow: inherit;
  -ms-box-shadow: inherit;
  -o-box-shadow: inherit;
}

ul,li {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

nav li a, a:active, a:focus, a:hover {
  outline: none;
  text-decoration: none;
  color: #fff;
}

b, strong {
  font-weight: 900;
  line-height: 2;
}
span{
  font-weight: 400;
  font-size: 13px;
}

 .btn.focus, 
 .btn:active:focus, 
 .btn:active:hover, 
 .btn:focus, 
 .btn:hover {
  outline: 0;
}

input {
  border: 1px solid #e7e7e7;
  border-radius: inherit;
  box-shadow: inherit;
  -webkit-box-shadow: inherit;
  -moz-box-shadow: inherit;
  -ms-box-shadow: inherit;
  -o-box-shadow: inherit;
  min-height: 50px;
}
/*__________top header __________*/

.header-top-icon ul {
  text-align: center;
  margin-top: 5px;
  display: flex !important;
}
.header-top-icon ul li:nth-child(1) {
  margin-left: 0px;
}

.header-top-icon ul li {
  margin-left: 20px;
  display: inline-block;
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
}

/* ---------common css class-------- */
/* i {
  margin-right: 10px;
} */
.mb-10{
  margin-bottom: 10px;
}
.p-50{
  padding: 50px;
}
.mt-10{
  margin-top: 10px;
}
.rounded-border {
    border-radius: 1rem !important
}

.text-small {
    font-size: 0.9rem !important
}
.common-title h2{
  color: #000;
}
.b-b-default {
  border-bottom: 1px solid #e0e0e0
}

.m-b-20 {
  margin-bottom: 20px
}

.p-b-5 {
  padding-bottom: 5px !important
}

.card .card-block p {
  line-height: 25px
}

.m-b-10 {
  margin-bottom: 10px
}

.text-muted {
  color: #919aa3 !important
}

.b-b-default {
  border-bottom: 1px solid #e0e0e0
}

.f-w-600 {
  font-weight: 600
}
.l-height-1{
line-height: 1;
}
.m-b-20 {
  margin-bottom: 30px
}

.m-t-40 {
  margin-top: 20px
}

.p-b-5 {
  padding-bottom: 5px !important
}

.m-b-10 {
  margin-bottom: 10px
}

.m-t-20 {
  margin-top: 20px
}
.w-100{
  width: 100% !important;
}

/*----------multi-level-accordian-menu------------*/
.navbar-logo{
  padding: 15px;
  color: #fff;
}
.nav-search-btn{
  color: #246df3;
  background-color: transparent;
  background-image: none;
  border-color: #ffffff;
  padding: 5px 10px 0px 10px;

}

.navbar-mainbg{
  background-color: #5161ce;
  padding: 0px;
}
#navbarSupportedContent{
  overflow: hidden;
  position: relative;
}
#navbarSupportedContent ul{
  padding: 0px;
  margin: 0px;
}
#navbarSupportedContent ul li a i{
  margin-right: 10px;
}
#navbarSupportedContent li {
  list-style-type: none;
  float: left;
}
#navbarSupportedContent ul li a{
  color: rgba(255,255,255,0.5);
    text-decoration: none;
    font-size: 15px;
    display: block;
    padding: 20px 20px;
    transition-duration:0.6s;
  transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    position: relative;
}
#navbarSupportedContent>ul>li.active>a{
  color: #5161ce;
  background-color: transparent;
  transition: all 0.7s;
}
#navbarSupportedContent a:not(:only-child):after {
  content: "\f105";
  position: absolute;
  right: 20px;
  top: 10px;
  font-size: 14px;
  font-family: "Font Awesome 5 Free";
  display: inline-block;
  padding-right: 3px;
  vertical-align: middle;
  font-weight: 900;
  transition: 0.5s;
}
#navbarSupportedContent .active>a:not(:only-child):after {
  transform: rotate(90deg);
}
.hori-selector{
  display:inline-block;
  position:absolute;
  height: 100%;
  top: 0px;
  left: 0px;
  transition-duration:0.6s;
  transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  background-color: #fff;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  margin-top: 10px;
}
.hori-selector .right,
.hori-selector .left{
  position: absolute;
  width: 25px;
  height: 25px;
  background-color: #fff;
  bottom: 10px;
}
.hori-selector .right{
  right: -25px;
}
.hori-selector .left{
  left: -25px;
}
.hori-selector .right:before,
.hori-selector .left:before{
  content: '';
    position: absolute;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #5161ce;
}
.hori-selector .right:before{
  bottom: 0;
    right: -25px;
}
.hori-selector .left:before{
  bottom: 0;
    left: -25px;
}


/*__________top header_________*/

.header-top {
  background-color: #4fcaf9f5;
  padding: 10px;
}
.top-info ul{
  display: inline-flex;
/*  padding: 15px 0px 10px 30px;*/
  color: #fff;
}

ul.top-bar-text li {
    list-style: none;
    display: inline-block;
    margin-right: 20px;
    color: #fff;
}

ul.social li a i.fa {
    height: 30px;
    width: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50%;
    transition: all 0.5s ease 0s;
    margin-right: -10px;
}

a, .icon-color {
    color: #fff;
}

ul.social li a.active i.fa, ul.social li a i.fa:hover {
    background-color: #009EFF;
    color: #fff;
}

ul.social-icon li:hover {
    color: #009EFF;
}

.top-info ul span{
  margin-right: 25px;
}
/*_________header_______*/
.header-top-contact ul li svg path{
  fill: #fff;
}
.btn-top{
  background-color: #009EFF !important;
}
.header-top-icon {
  padding: 5px;
}
.header_top_area{
  background: #3d4352;
  padding: 10px;
}
.header_wrapper ul li{
  color: #ffffff;
  margin-right: 20px;
}
.header_wrapper ul li i{
  margin-right: 10px;
}
.header_social{
  margin-right: 9%;
}
.header_social a{
  margin-right: 15px;
  color: #ffffff;
}

/*start preloader css*/


#preloader {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  position: fixed;
  overflow: visible;
  background: #fff url('../image/preloder.gif') no-repeat center center;
  animation: loader 5s infinite ease;
}

/*________naavbar_______*/
.dropdown-toggle::after {
  display: none;
}
.navbar-bg-color {
  background-color: #0eb3f3;
}
.text-decoration-none{
  text-decoration: none!important;
}
/* Font Awesome Icons have variable width. Added fixed width to fix that.*/
.icon-width { 
  width: 2rem;
}
.top-info {
  display: inline-block;
  width: 100%;
  float: left;
  background: #f9f9f9;
  height: 50px;
  padding-top: 10px;
  border-bottom: 1px solid #e6e6e6;
}
.top-info .personal-info {
  color: #999999;
  float: left;
}
.top-info .personal-info li:nth-child(1) {
  padding-left: 0px;
}
.top-info .personal-info li {
  display: inline-block;
  border-right: 1px solid #ececec;
  padding: 1px 20px;
  float: left;
}
.top-info .personal-info p {
  color: #333333;
  margin: 0px;
  font-size: 14px;
}
.navbar-bg-color {
  background-color: #4bc7f9;
}
.nav-item a {
  font-weight: 500;
}

/* ==== */

.navbar-links {
  list-style-type: none;
  display: flex;
}
.navbar-links li a {
  display: block;
  text-decoration: none;
  color: rgb(255, 255, 255);
  padding: 20px 20px;
  font-weight: 600;
  transition: 0.4s all;
}

.navbar-links li.navbar-dropdown {
  position: relative;
}

.navbar-links li.navbar-dropdown:hover .dropdown {
  visibility: visible;
  opacity: 1;
  transform: translateY(0px);
}

.navbar-links li.navbar-dropdown .dropdown {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  top: 100%;
  transform: translateY(50px);
  left: 0;
  width: 180px;
  background-color: #fff;
  box-shadow: 0px 10px 10px 3px rgba(0, 0, 0, 0.3);
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  z-index: 111;
  transition: 0.4s all;
}
.navbar-links li.navbar-dropdown .dropdown a {
  padding-top: 6px;
  padding-bottom: 6px;
  font-weight: 400;
  color: #000;
}
.navbar-dropdown .dropdown a:hover {
  padding-left: 30px;
  background-color: #4bc7f9;
  color: #fff !important;
}
.navbar-links li a:hover {
  color: rgb(0, 0, 0);
}
.nav-icons a svg path{
  fill: #fff;
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
}
.nav-item:hover .nav-link svg path{
  fill: #000;
}
/* ------mobile menu navbar------ */


/*====================================
          banner - start
====================================*/
#slider-banner{
  position: relative;
}

.carousel-item img {
  position: relative;
  height: auto;
  max-height: 100% !important;
}
.slider-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
} 
.slider-text{
  position: absolute;
}
.slider-content {
  position: absolute;
  display: block;
  top: 50%;
  width: 100%;
  line-height: 4rem;
  transform: translateY(-260%);
}

.slider-content p{
  margin-bottom: 40px;
  color: #f7f7f7;
  text-align: left;
}
.slider-content h1{
  margin-bottom: 30px;
  color: #fff !important;
}
.slider-content h2 span{
  color: #5DADE2 ;
}

.carousel-indicators {
  position: absolute;
  bottom: 5%;
  z-index: 9;
  right: 0%;
  transform: rotate(180deg);
}
.carousel-indicators li{
  width: 10px;
  height: 10px;
  background: #fff;
  border-radius: 500px;
  transform: rotate(180deg);
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  cursor: pointer;
}
.carousel-indicators li.active{
  background:#0eb3f3;
  width: 12px;
  height: 12px;
  margin-top: -1px;
}
.carousel-item.active
.slider-content .slidp1{
  font-size: 18px;
  font-weight: 500;
  color: #259ed9;
}
.slider-content h2{
  font-size: 37px;
  font-weight: bolder;
  color: #ffffff;
  line-height: 4rem;
}
.slider-content .slidp2{
    color: #ffffff;
}
.slider-content a{
  background:#46bbffd1;
  border: none;
  padding: 15px 30px;
  margin-right: 10px;
  border-radius: 0px;
  color: #ffffff;
  transition: all 0.5s ease-in-out;
}
.slider-content a:hover{
  background: #00000000;
  color: #dee0e0;
  border: 1px solid #ffffff;
}
#slider-banner::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 35%;
  z-index: -1;
  background-color: #fff;
}
.shape{
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}
.shape svg{
  width: 100%;
}

/*====================================
         banner - end
====================================*/
/*----Promo section----*/
#bit-job-impact-area{
  position: relative;
  z-index: 1;
  background-image: url(../image/area-background.png);
  background-size: cover;
  background-color: #000000b9;
  background-repeat: no-repeat;
  height: 100%;
  background-color: var(--mainColor);
}
#bit-job-impact-area:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 35%;
  z-index: -1;
  background-color: #fff;
}
#bit-job-impact-area .main-area{
  position: absolute;
  top: 47%;
  left: 15%;
  right: 15%;
}
.area-svg-icon svg{
  padding: 20px;
  width: 100px;
  height: 100px;
}

.area-content {
  background-color: #f7f7f7;
  color: #000;
  padding: 18px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
}

.area-content p{
  margin-bottom: 10px;
}

.area-content:hover{
  background-color: #259ed9;
}
.area-content:hover h4{
  color: #fff;
}
.area-content:hover p{
  color: rgb(241, 241, 241);
}
.area-content:hover .area-svg-icon svg path{
  fill: #ffffff;
}
.offer-area,
.offer-pera{
  color: #fff;
}

.area-text h4{
  color: #259ed9;
  font-weight: 600;
}

.area-svg-icon svg path{
  fill: #259ed9;
}
.area-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -9;
  background-color: #00000087;
}
/*----Promo section end----*/

/*-----------under heading -------*/
.heading {
  text-align: center;
  color: #454343;
  font-size: 30px;
  font-weight: 700;
  position: relative;
  margin-bottom: 45px;
  text-transform: uppercase;
  z-index: 999;
}
.white-heading{
    color: #000;
}
.heading:after {
  content: ' ';
  position: absolute;
  top: 100%;
  left: 50%;
  height: 40px;
  width: 180px;
  border-radius: 4px;
  transform: translateX(-50%);
  background: url(../image/heading-line-white.png);
  background-repeat: no-repeat;
  background-position: center;
}
.white-heading:after {
  background: url(../image/heading-line-white.png);
  background-repeat: no-repeat;
  background-position: center;
}

.heading span {
  font-size: 18px;
  display: block;
  font-weight: 500;
}
.white-heading span {
  color: #000;
}

/*media*/
@media (max-width:574px){
  .testimonials .slick-list.draggable {padding: 0!important;}
}
/*===================================
              aabout us
======================================*/

.section-tittle h2 span{
  color: #0eafec;
}
.about-text{
  line-height: 1.5rem;
}
.about-text p{
  margin-bottom: 25px;
}
.about-text h5{
  font-weight: 700;
  font-size: 1.6rem;
}
.about-btn a{
  background-color: #007bff;
  padding: 12px 22px;
  color: #ffff;
}
.about-icon svg path{
  fill: #0eafec;
}
.about-btn {
  margin-top: 45px;
}
.blockquote {
  padding: 20px 20px;
  margin-bottom: 20px;
  margin-top: 20px;
  background-color: rgba(69, 90, 100, 0.08);
  border-left: 2px solid #419db8;
}
.blockquote p {
  font-size: 16px;
  color: #5a5a5a;
  font-style: italic;
}
.about-content ul {
  margin-bottom: 15px;
  margin-top: 25px;
}
.about-text .about-content ul li {
  text-align: center;
  padding-inline: 40px;
}
.about-text .about-content ul li .icon, .about-area .about-content ul li .info {
  display: table-cell;
  vertical-align: top;
}
.about-text .about-content ul li .icon i {
  color: #1cb9c8;
  font-size: 40px;
}
.about-area .about-content ul li .icon, .about-area .about-content ul li .info {
  display: table-cell;
  vertical-align: top;
}
.info {
  display: inline-grid;
}
.about-content ul{
  display: inline-flex;
}

/*=-------=*/
.heading .divider-left {
    margin: 10px 0 -2px;
}
/*button*/
.button-div {
  display: grid;
  place-items: flex-start;
}

.button-div a {
  text-decoration: none;
  font-size: 1rem;
  padding: 8px 2rem;
  border-radius: 4px;
  color: #fff;
  background-color: #0eafec;
  position: relative;
  overflow: hidden !important;
  z-index: 1;
  transition: all 0.4s linear;
  -webkit-transition: all 0.4s linear;
  -moz-transition: all 0.4s linear;
  -ms-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
}

.button-div a::before {
  content: '';
  position: absolute;
  background-color: #000;
  width: 100%;
  height: 100%;
  top: 0;
  left: -1px;
  transform: translateX(-100%);
  transition: all 0.3s linear
}

.button-div a:hover:before {
  transform: translateX(0%);
  z-index: -1
}

.button-div a:hover {
  color: #fff;
  border: none;
}


/*=====================================
              about us -end
=======================================*/
/*======================================
              features
=========================================*/
.service-title h4{
  margin-bottom: 0px;
  line-height: 2.5rem;
  color: #000;
}
.feature-item{
  margin-bottom: 10px;
}
.service-title{
  margin: 0px 10px 15px 10px;
}
.service-title h6,
.service-title p{
  text-align: center;
}

.feature-content{
  padding: 30px;
  margin-bottom: 40px;
  transition: all ease-in-out .5s;
}
.feature-content p{
  margin-bottom: 0px;
  text-align: center;
}
.feature-content:hover{
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.feature-content svg path{
  fill: #0eafec;
}

/*======================================
              features - end
=========================================*/

/*====================================
 counter - start
====================================*/

#counter{
  background-image: url(../image/counter-image.png);
  background-size: cover;
  background-attachment: fixed;
  background-repeat: no-repeat;
  position: relative;
  z-index: -2;
  background-position: center;
  padding: 80px 0px 50px;
}
.overlay-counter{
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.6);
  z-index: -1;
}
.number-icons {
  width: 70px;
  height: 55px;
  display: inline-block;
  padding: 10px;
}
.number-icons svg path{
  fill: #0eafec;
}
.number-icons i{
  color: #b89841;
  margin-top: 50%;
  transform: translateY(-50%);
  font-size: 40px;

}
.counter-item .number-icons svg:not(:root).svg-inline--fa {
  overflow: visible;
  color: #0eafec;
  font-size: 40px;
  }

.tabs svg:not(:root).svg-inline--fa {
  margin-right: 5px;
}

.number-no {
  color: #ffffff;
  font-size: 36px;
  font-weight: 700;
  letter-spacing: 0.01em;
  margin-bottom: 0px;

}
.number-title{
  color: #ffffff;
  font-size: 15px;
  font-weight: 500; 
}

.counter-item h3 span {
  font-size: 30px;
  font-weight: bold;
  font-style: normal;
}

#navbarNav ul li a svg:not(:root).svg-inline--fa {
  overflow: visible;
  color: #fff;
}
/*====================================
 counter - end
====================================*/

/*====================================
 portfolio - start
====================================*/

.portfolio-title .bottom-style{
   margin: 0px 10px 15px 10px;
}
.portfolio-details{
  border-radius: 10px;
  position: relative;
}
.portfolio-btn{
  margin-left: 50%;
  transform: translateX(-50%);
  background: #b89841;
  color: #ffffff;
}
.portfolio-contant{
  text-align: center;
  margin-top: 50%;
  transform: translateY(-50%);
}
.portfolio-contant h5{
  color: #ffffff;
  margin-bottom: 10px;
}
.portfolio-contant p{
  color: #ffffff;
}

.portfolio-details {
  position: relative;
  overflow: hidden;
}

.portfolio-details img {
  display: block;
  width: 100%;
  height: auto;
}

.port-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  background: rgba(64, 175, 255, 0.6);
  opacity: 0;
  transition: width 0.3s ease, height 0.3s ease, opacity 0.3s ease;
}

.portfolio-details:hover .port-overlay {
  width: 100%;
  height: 100%;
  opacity: 1;
}

.port-overlay i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 32px;
  color: #fff;
}


.portfolio-btn:hover{
  color: #ffffff;
}
.nav-pills li a{
  background: none !important;
  color: #3d4352;
  font-weight: 600;
}
.nav-pills .nav-link.active, 
.nav-pills .show>.nav-link {
  color: #099ec4;
  border: 1px solid;
}

/*====================================
 portfolio - end
====================================*/


/*-------demo vedio------*/

#vedio-demo{
  background-image: url(../image/demo-vedio.png);
  width: auto;
  background-size: cover;
  background-attachment: fixed;
  background-repeat: no-repeat;
  position: relative;
  z-index: -2;
  background-position: center;
}
.vedio-overlay{
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(0deg, rgba(35,159,194,0.8323704481792717) 0%,
   rgba(0,0,0,0.4990371148459384) 0%, rgba(0,0,0,0.6643032212885154) 0%,
    rgba(0,0,0,0.6110819327731092) 9%, rgba(0,0,0,0.5998774509803921) 18%,
   rgba(11,173,219,0.639093137254902) 100%);
}
.vedio-title h2{
  color: #fff;
}
.vedio-title p{
  color: rgb(221, 217, 217);
}

.vedio-title p{
  padding: 0px 100px;
}
a .icon-md svg path{
  fill: #fff;
}
.download-btn-wrap a.btn:hover {
background: linear-gradient(0deg, rgba(107, 170, 187, 0.832) 0%,
 rgba(0,0,0,0.4990371148459384) 0%,
  rgba(82, 135, 148, 0.872) 1%,
   rgba(106, 143, 155, 0.69) 9%,
    rgba(121, 152, 168, 0.6) 21%,
 rgba(0,0,0,0.3337710084033614) 100%);
    color: #fff !important;
}
.download-btn-wrap a.btn{
 background: transparent;
 transition: all 0.5s ease-in-out; 
}

/*-------demo vedio end-----*/

/*_______Team section________*/
.team-content {
    background: #fff;
    border: 1px solid #e0dddd;
    transition: all 0.5s ease-in-out;
}
.hvr-item{
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
}
.social-link {
  width: 40px;
  height: 40px;
  outline: 0 !important;
  cursor: pointer;
  font-size: 25px;
  border: none;
  margin-right: 10px;
  background: linear-gradient(145deg, #d6d6d6, #ffffff);
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
.social-buttons{
  margin-top: 2.5rem!important;
}
.social-link:hover {
  background: #f7f7f7;
}

.social-link svg path{
  fill: #0eafec;
}

.social-buttons:hover{
  background: linear-gradient(145deg,#ffffff, #d6d6d6);
}
.social-link:active {
  border-radius: 50%;
  background: linear-gradient(145deg, #d6d6d6, #ffffff);
  box-shadow: inset 20px 20px 60px #cacaca, inset -20px -20px 60px #ffffff
}

.hvr-item:hover{
  transform: translateY(-10px);
}


/* ---- */
.box1 img,
.box1:after,
.box1:before {
    width: 100%;
    transition: all .3s ease 0s
}

.box1 .icon,
.box2,
.box3,
.box4,
.box5 .icon li a {
    text-align: center
}

.box10:after,
.box10:before,
.box1:after,
.box1:before,
.box2 .inner-content:after,
.box3:after,
.box3:before,
.box4:before,
.box5:after,
.box5:before,
.box6:after,
.box7:after,
.box7:before {
    content: ""
}

.box1,
.box11,
.box12,
.box13,
.box14,
.box16,
.box17,
.box18,
.box2,
.box20,
.box21,
.box3,
.box4,
.box5,
.box5 .icon li a,
.box6,
.box7,
.box8 {
    overflow: hidden
}

.box1 .title,
.box10 .title,
.box4 .title,
.box7 .title {
    letter-spacing: 1px
}

.box3 .post,
.box4 .post,
.box5 .post,
.box7 .post {
    font-style: italic
}

.mt-30 {
    margin-top: 30px
}

.mt-40 {
    margin-top: 40px
}

.mb-30 {
    margin-bottom: 30px
}

.box1 .icon,
.box1 .title {
    margin: 0;
    position: absolute
}

.box1 {
    box-shadow: 0 0 3px rgba(0, 0, 0, .3);
    position: relative
}

.box1:after,
.box1:before {
    height: 50%;
    background: rgba(0, 0, 0, .5);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    transform-origin: 100% 0;
    transform: rotateZ(90deg)
}

.box1:after {
    top: auto;
    bottom: 0;
    transform-origin: 0 100%
}

.box1:hover:after,
.box1:hover:before {
    transform: rotateZ(0)
}

.box1 img {
    height: auto;
    transform: scale(1) rotate(0)
}

.box1:hover img {
    filter: sepia(80%);
    transform: scale(1.3) rotate(10deg)
}

.box1 .title {
    font-size: 19px;
    font-weight: 600;
    color: #fff;
    line-height: 2rem;
    text-shadow: 0 0 1px #004cbf;
    bottom: 15px;
    left: 30px;
    opacity: 0;
    z-index: 2;
    transform: scale(0);
    transition: all .5s ease .2s
}

.box1:hover .title {
    opacity: 1;
    transform: scale(1)
}

.box1 .icon {
  padding: 7px 5px;
  list-style: none;
  background: #fffffff6;
  border-radius: 0 0 0 10px;
  top: -100%;
  right: 0;
  z-index: 2;
  transition: all .3s ease .2s;
  -webkit-transition: all .3s ease .2s;
  -moz-transition: all .3s ease .2s;
  -ms-transition: all .3s ease .2s;
  -o-transition: all .3s ease .2s;
}

.box1:hover .icon {
  top: 0
}

.box1 .icon li {
  display: block;
  margin: 10px 0
}

.box1 .icon li a {
  display: block;
  width: 35px;
  height: 35px;
  line-height: 35px;
  border-radius: 10px;
  font-size: 18px;
  color: #fff;
  transition: all .3s ease 0s
}

.box2 .icon li a,
.box3 .icon a:hover,
.box4 .icon li a:hover,
.box5 .icon li a,
.box6 .icon li a {
    border-radius: 50%
}

.box1 .icon li a:hover {
    padding: 5px;
}

/*_______Team section end________*/

/* _________Team details_________ */

.padding {
  padding: 3rem !important
}

.user-card-full {
  overflow: hidden
}

.card {
  border-radius: 5px;
  -webkit-box-shadow: 0 1px 20px 0 rgba(69, 90, 100, 0.08);
  box-shadow: 0 1px 20px 0 rgba(69, 90, 100, 0.08);
  border: none;
  margin-bottom: 30px
}

.m-r-0 {
  margin-right: 0px
}

.m-l-0 {
  margin-left: 0px
}

.user-card-full .user-profile {
  border-radius: 5px 0 0 5px
}

.bg-c-lite-green {
  background: -webkit-gradient(linear, left top, right top, from(#f29263), to(#ee5a6f));
  background: linear-gradient(to right, #ee5a6f, #f29263)
}

.user-profile {
  padding: 20px 0
}

.card-block {
  padding: 1.25rem
}

.m-b-25 {
  margin-bottom: 25px
}

.img-radius {
  border-radius: 5px
}

h6 {
  font-size: 14px
}

.card .card-block p {
  line-height: 25px
}

@media only screen and (min-width: 1400px) {
  p {
      font-size: 14px
  }
}

.card-block {
  padding: 1.25rem
}


.user-card-full .social-link li {
  display: inline-block
}

.user-card-full .social-link li a {
  font-size: 20px;
  margin: 0 10px 0 0;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out
}

/*-------Pricing Secction--------*/

.pricing {
    color: #fff
}

.underline {
  width: 5rem;
  height: 6px;
  border-radius: 1rem;
  background-color: #0eafec;
}

.text-uppercase {
    letter-spacing: 0.2em
}

.table-card-2 .btn-item a{
  background-color: #0eafec;
}
.table-card-2 .btn-item:hover a{
  background-color: rgb(232 232 232 / 67%);
  color: rgb(0 0 0);
  border: 1px solid rgb(1 165 253 / 87%);
}

.pricing-table{
  border: 1px solid #e6dede;
}
.table-card-2{
  border: 2px solid #0eafec;
}

.table-card-1 .btn-item a,
.table-card-3 .btn-item a {
  background-color: rgb(232 232 232 / 67%);
  color: rgb(0 0 0);
  border: 1px solid rgb(1 165 253 / 87%);
}
/*-------Pricing section end------*/

/*-------- Pratner SEction --------*/
#brand-section{
  padding: 80px 0px; 
}
.client .owl-item img {
  display: block;
  width: 70%;
}
.client .client-img{
  display: flex;
  justify-content: center;
  padding: 0px 0px;
  filter: grayscale(100%);
  opacity: 0.4;
}
.client .owl-nav .owl-prev{
  position: absolute;
  left: -5%;
  top: 20px;
  width: 40px;
  height: 40px;
  border: 1px solid  #0eafec !important;
  transition: all 0.5s ease-in-out;
}
.client .owl-nav .owl-prev:hover{
  background:  #0eafec !important;
  cursor: pointer;
}
.client .owl-nav .owl-prev span{
  color:  #0eafec !important;
  font-size: 32px;
  transition: all 0.5s ease-in-out;
}
.client .owl-nav .owl-prev:hover span{
  color: #fff !important;
}
.client .owl-nav .owl-next{
  position: absolute;
  right: -5%;
  top: 20px;
  width: 40px;
  height: 40px;
  border: 1px solid  #0eafec !important;
  transition: all 0.5s ease-in-out;
}
.client .owl-nav .owl-next:hover{
  background: #0eafec !important;
  cursor: pointer;
}
.client .owl-nav .owl-next span{
  color:  #0eafec !important;
  font-size: 32px;
  transition: all 0.5s ease-in-out;
}
.client .owl-nav .owl-next:hover span{
  color:  #ffff !important;
}
.owl-theme .owl-dots, .owl-theme .owl-nav {
  text-align: center;
  -webkit-tap-highlight-color: transparent;
  display: none;
}

/*--------Pratner section end -------*/

/*--------BLog section-------*/

.blog-details .blog-card {
  width: 100%;
  height: auto;
  margin: 0 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.blog-card img {
  width: 100%;
  border-radius: 5px;
  transition: all 0.4s ease;
  z-index: 0;
}
.blog-image img{
  position: relative;
}

.blog-infos {
  width: 100%;
  background: #fff;
  top: 80%;
  border-radius: 5px;
  padding: 20px 0px;
  transition: all 0.4s ease;
  z-index: 1;
}

.blog-date {
  padding: 0px 14px 7px;
  text-align: center;
  position: absolute;
  top: 0%;
  right: 5%;
  color: #fff;
}

.blog-date h5{
  margin-bottom: 0px;
  color: #fff;
}

.blog-date {
  background-color: #009EFF;
  border-radius: 3px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.blog-infos h4 a {
  color: #000000;
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
}
.blog-infos:hover h4 a{
  color: #009EFF;
}
.blog-date{
  opacity: 0;
  height: 0px;
  transition: height 1s;
  -webkit-transition: height 1s;
  -moz-transition: height 1s;
  -ms-transition: height 1s;
  -o-transition: height 1s;
}
.blog-card:hover .blog-date {
  opacity: 1;
  height: 70px;
}
.blog-admin-details img{
  width: 50px!important;
  height: 50px;
  position: relative;
  margin-bottom: 10px;
}
.subscribe i{
  color: #000;
}
.blog-admin-details h6{
  color: #000;
  line-height: 0rem;
  margin-bottom: 0.5rem;
}

.blog-infos .blog-meta {
  border-top: 1px solid #e7e7e7;
  float: left;
  padding-top: 15px;
  width: 100%;
  margin-bottom: -5px;
}
.blog-infos .blog-meta li {
  color: #666666;
  float: left;
  font-weight: 200;
  letter-spacing: 1px;
}
.blog-infos h4{
  margin-bottom: .5rem;
}
.blog-meta a {
  color: #ffffff;
  float: right;
  font-weight: 600;
  background-color: #0eafec;
  border: 1px solid #0eafec;
  padding: 4px 10px;
  border-radius: 4px;
  transition: all 0.4s ease-in-out;
}
.blog-meta a:hover {
  color: #7A7979;
  background-color: #fff;
}
.btn-item:hover a{
  background-color: #0eafec;
  color: #fff;
}
/*--------Blog SEction end--------*/

/* ---------- */
/*---blog details---*/
.archive_content ul li i{
  padding-top: 5px;

}
.archive_content ul .archive-text{
  padding-left: 10px;
}
.archive_content{
  border: 1px solid #a1a1a145;
  margin-top: 30px;
  padding: 30px;
}
.archive_content ul{
  cursor: pointer;
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
}
.archive_content ul:hover {
  margin-left: 5px;
}
.blg-pera{
  padding-left: 15px;
}
.bg_pera{
  background-color: #349bfcda ;
  color: #fff;
  padding: 15px;
  font-size: 16px;
  font-style: italic;
}
.details-right h5 a{
  color:#48453d;
  line-height: 1.5;
  font-size: 18px;
  font-weight: 500;
}
.details-right h5{
  line-height: 24px;
}
.blog-reset{
  opacity: 0;
}
.btn-post-comment {
  background-color: #b9bff7a1;
}
.blog_item span {
  padding: 10px 12px;
  font-size: 22px;
  background-color: #4bc7f9;
  color: #fff;
  border-radius: 4px;
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.tags_item {
  border: 1px solid #f1f1f1;
  color: #48453d;
  margin-top: 30px;
  overflow: hidden;
}


/* ------footer section ----------- */
.footer-widget ul {
    margin: 0px;
    padding: 0px;
}
.single-cta svg path{
  fill: #0eafec;
}
.footer-section {
  background: #151414;
  position: relative;
}
.footer-cta {
  border-bottom: 1px solid #373636;
}
.single-cta i {
  color: #ff5e14;
  font-size: 30px;
  float: left;
  margin-top: 8px;
}
.cta-text {
  padding-left: 15px;
  display: inline-block;
}
.cta-text h4 {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 2px;
}
.cta-text span {
  color: #757575;
  font-size: 15px;
}
.footer-content {
  position: relative;
  z-index: 2;
}
.footer-pattern img {
  position: absolute;
  top: 0;
  left: 0;
  height: 330px;
  background-size: cover;
  background-position: 100% 100%;
}
.footer-logo {
  margin-bottom: 30px;
}
.footer-logo img {
    max-width: 130px;
}
.footer-text p {
  margin-bottom: 14px;
  font-size: 14px;
      color: #7e7e7e;
  line-height: 28px;
}
.footer-social-icon span {
  color: #fff;
  display: block;
  font-size: 20px;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
  margin-bottom: 20px;
}
.footer-social-icon a {
  color: #fff;
  font-size: 16px;
  margin-right: 15px;
}
.footer-social-icon i {
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 38px;
  border-radius: 50%;
}
.facebook-bg{
  background: #3B5998;
}
.twitter-bg{
  background: #55ACEE;
}
.google-bg{
  background: #DD4B39;
}
.footer-widget-heading h3 {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 40px;
  position: relative;
}
.footer-widget-heading h3::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: -15px;
  height: 2px;
  width: 50px;
  background: #55ACEE;
}
.footer-widget ul li {
  display: inline-block;
  float: left;
  width: 50%;
  margin-bottom: 12px;
}
.footer-widget ul li a:hover{
  color: #55ACEE;
}
.footer-widget ul li a {
  color: #878787;
  text-transform: capitalize;
}
.subscribe-form {
  position: relative;
  overflow: hidden;
}
.subscribe-form input {
  width: 100%;
  padding: 14px 28px;
  background: #2E2E2E;
  border: 1px solid #2E2E2E;
  color: #fff;
}
.subscribe-form button {
  position: absolute;
  right: 0;
  background: #55ACEE;
  padding: 13px 20px;
  border: 1px solid #55ACEE;
  top: 0;
}
.subscribe-form button:focus{
  outline: none;
}
.subscribe-form button i {
  color: #fff;
  font-size: 22px;
  transform: rotate(-6deg);
}
.copyright-area{
  background: #202020;
  padding: 25px 0;
}
.copyright-text p {
  margin: 0;
  font-size: 14px;
  color: #878787;
}
.copyright-text p a{
  color: #55ACEE;
}
.footer-menu li {
  display: inline-block;
  margin-left: 20px;
}
.footer-menu li:hover a{
  color: #55ACEE;
}
.footer-menu li a {
  font-size: 14px;
  color: #878787;
}
.footer-cta{
  padding: 80px 0px 18px;
}
/* ---- */
footer .scrollToTop {
  position: fixed;
  width: 50px;
  height: 50px;
  right: 25px;
  bottom: 6px;
  background-color: #108bec;
  border-radius: 50px;
  padding: 10px 10px;
  z-index: 999;
  border: 1px solid #ffffff;
}
footer .scrollToTop svg path {
  fill: #fff;

}

/* ---------footer section end -------- */

/*----------page breadcrumb---------*/

#page-breadcrumb {
  position: relative;
  background-image: url(../image/breadcrumb-image.png);
  z-index: 1;
  background-size: cover;
  background-position: center;
  padding: 60px 0px;
}
.common-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(82deg, rgba(14,175,236,1) 0%, rgba(14,175,236,1) 0%, rgb(22 161 214 / 95%) 31%, rgb(14 175 236 / 70%) 43%, rgb(14 175 236 / 37%) 53%, rgb(14 40 49 / 60%) 61%, rgb(6 19 25 / 48%) 72%, rgb(15 58 74 / 44%) 83%, rgb(0 0 0 / 29%) 90%);
  z-index: 0;
}
.breadcrumb-menu {
  color: #ffffff;
  position: relative;
  text-transform: capitalize;
  text-align: center;
  padding-top: 40px;
}
.detsils-breadcrumb{
  color: #ffffff;
  position: absolute;
  top: 62%;
  left: 28%;
  transform: translate(-50%, -50%);
  text-transform: capitalize;
  text-align: center;
  padding-top: 40px;
}
.breadcrumb-menu ul li svg {
  height: 20px;
  padding: 8px 0px 0px 0px;
  width: 25px;
}
.detsils-breadcrumb ul li svg{
  height: 20px;
  padding: 8px 0px 0px 0px;
  width: 25px;
}
.breadcrumb-menu h3 {
  text-align: left;
  font-size: 34px;
  font-weight: bold;
  text-transform: capitalize;
  color: #ffffff;
  margin-bottom: 15px;
}
.breadcrumb-menu .breadcrumb {
  background-color: transparent;
  border-style: none;
  border-radius: 0px;
  padding: 0;
}
.breadcrumb>li {
  display: inline-block;
}
.breadcrumb .list1 a{
  color: #c3ebff; 
}
.breadcrumb li svg path{
  fill: #f7f7f7;
}

#about-section{
  background-color:white;
}
.about_para h5{
  color: #3b18c6;
}
/*----------page breadcrumb end-------*/

/*--------about details --------*/
/* new platform */
.bit-platform-head small{
  color: #000 ;
}
.text-color {
  color: #fff
}
.platform-slide .slick-slide {
  width: 100%!important;
  height: 100%;
  min-height: 0px;
  float: none;
}
.lead{
  font-size: 16px!important;
  margin-bottom: 30px;
}
.main-platform {
  background-color: #fff;
  padding: 25px;
  border-radius: 5px;
  max-width: 630px;
}
.inner-heading{
  font-size: 20px;
  font-weight: 500;
  color: #000;
}
.main-platform .carousel{
  width: 100%;
  height: 100%;
  padding-bottom: 0px;
}
.btn-slide {
  border-radius: 3px;
  padding: 8px 15px;
  height: 40px;
  width: 106px;
  background-color: #0eafec;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
}
.btn-slide a{
  color: #fff;
}
.btn-slide:hover {
  background-color: #243c4f!important;
}

.about-area .about-list li .icon, .about-area .about-list li .info {
    display: table-cell;
    vertical-align: top;
}
.others-icon {
    display: inline-flex;
    padding: 20px 25px 0px 0px;
}
.icon-shape svg path{
  fill: #ffffff;
}
.others-icon svg path{
  fill: rgb(41, 160, 240);
}
.icon-shape{
  background-color: #0eafec;
}
/* start testimonails */
/*testimonial*/
.testimonial-area {
  background: url(../Image/testimonials-background.png) no-repeat;
  padding: 80px 0;
  background-size: cover;
  background-position: center;
  background-attachment: fixed; 
  position: relative;
}
.testimonials-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0,0,0,0.6530987394957983) 0%, rgba(11,45,57,0.7539390756302521) 21%, rgba(42,173,217,0.5690651260504201) 97%);
  z-index: 0;
}
.testimonial-area p {
  color: rgb(255, 255, 255);
  font-weight: 500;
  font-size: 16px;
}
.testimonial-area .heading h2 {
  color: #000;}
.testimonial {
  padding: 20px;
  background: #fff;
  position: relative;
  transform: scale(0.8); 
  padding-top: 100px;
  margin-top: 100px; 
  transition: all 0.3s linear; 
  border-radius: 4px;
}
.testimonials .slick-slide.slick-current.slick-active.slick-center .testimonial {
  transform: scale(1);
}
.author-img {
  position: absolute;
  width: 150px; 
  max-height: 150px; 
  border-radius: 200px;
  overflow: hidden;
  border: 5px solid #fff;
  top: -75px;
  left: 0;
  right: 0;
  margin: auto; 
  box-shadow: 0 4px 10px rgba(0,0,0,0.1); 
  background: #fff;
}
.author-quote h4 {
  font-size: 25px;
  letter-spacing: 0.5px;
}
.author-quote {
  text-align: center;
}
.author-quote blockquote i {
  color: #ffbe00;
  font-size: 22px;
  padding: 0 5px;
}
.author-quote blockquote {
  line-height: 30px;
  color: #676767;
  margin: 15px 0;
}
ul.slick-dots li button {
  font-size: 0;
  height: 14px;
  width: 14px;
  padding: 0;
  border-radius: 100px;
  border: 0;
  background: #01a3e4;
  border: 2px solid #fff; 
  transition: all 0.3s ease;
}
ul.slick-dots li {
  display: inline-block;
  margin: 0 3px;
}
ul.slick-dots {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: center;
}
ul.slick-dots li.slick-active button {
  width: 30px;
}
.slick-arrow:after {
  content: "\f053";
  font-family: 'FontAwesome';
}
.slick-arrow {
  position: absolute;
  top: 0;
  bottom: 0;
  height: 40px;
  width: 40px;
  font-size: 0;
  margin: auto;
  border: 0;
  background: rgba(255,255,255,0.5); 
  z-index: 1;
  cursor: pointer;
}
.slick-next.slick-arrow{
  left:inherit ; 
}
.slick-next.slick-arrow:after{
  content:"\f054";
}
.testimonials .slick-list.draggable {
  padding: 0 20px !important;
}
/*end*/

/* endtestimonails */


/* ------service details---- */
.sidebar{
  position: sticky;
  position: -webkit-sticky;
}
.detail_course {
  background: #f2f2f2;
  padding: 20px 15px;
}
 .profile_bg {
  padding: 30px 0px 30px;
}
.profile {
  background: #fff;
  padding: 15px 0px 15px;
  display: table;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.detail_course {
  background: #f2f2f2;
  padding: 20px 15px;
}

.profile_border {
  border-top: 1px solid #c8c8c8;
}
.review-space{
  margin-bottom: 45px;
}
ul.bullet_list li {
  margin-bottom: 15px;
  margin-left: 10px;
}
ul.bullet_list li:before {
  content: '';
  height: 5px;
  width: 5px;
  margin-left: -10px;
  background: #5b5b5b;
  display: inline-block;
  vertical-align: middle;
  margin-right: 5px;
  -webkit-border-radius: 100%;
  border-radius: 100%;
}
.profile_text {
  padding-left: 15px; 
}
.profile .p_pic {
  width: 150px;
}
.client-dp{
  padding: 30px 0px 30px;
}
.client-dp p{
  margin-bottom: 10px;
}
.detail_course .info_label {
  color: #5b5b5b;
  display: inline-block;
  margin-right: 20px;
}
.detail_course {
  background: #f2f2f2;
  padding: 20px 15px;
}
.detail_course .icony {
  height: 32px;
  width: 32px;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  background: #73AE20;
  color: #fff;
  display: inline-block;
  float: left;
  line-height: 32px;
  font-size: 18px;
  text-align: center;
}
.review-left{
  border: 1px solid;
  padding: 20px;
}
.review-space{
  margin-bottom: 65px;
}
.star-rating p{
  font-size: 50px;
  padding: 20px;
  font-weight: 500;
  font-style: italic;
}
.rating_progress span {
  font-size: 14px;
  margin-top: -5px;
}
p.hours span {
  float: right;
}
.time-content p {
  margin-bottom: 15px;
}
.time-content p a{
  padding: 5px;
  border-radius: 6px;
  background-color: rgb(0 131 171);
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -ms-border-radius: 6px;
  -o-border-radius: 6px;
}
.feature-service a{
  display: inline-block;
  padding: 2px 8px 2px 8px;
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
}
.heading_space h3{
  margin-bottom: 20px;
}
.l-height-1{
  margin-bottom: 10px;
}
/*-----side content----*/
.widget .media {
  border-bottom: 1px solid #d5d5d5;
  padding-bottom: 20px;
  margin-bottom: 20px;
}
/* get started for free */
.back-cta {
  background: url(../Image/get-strated-free-bg.png) no-repeat center center;
  background-size: cover;
  padding: 100px 0 100px;
  position: relative;
}
.get-overlay{
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
180deg
, rgba(0,0,0,0.6530987394957983) 0%, rgba(11,45,57,0.7539390756302521) 21%, rgb(48 52 53 / 57%) 97%);
    z-index: 0;
  }

.back-cta .back-title {
  font-size: 45px;
  line-height: 1.2;
  letter-spacing: -1px;
  font-weight: 700;
  margin: 0 0 20px;
}
.back-cta .back-sec-btn a {
  padding: 21px 28px 19px;
}

.back-btn.white {
  background: #ffffff;
  border-color: #ffffff;
  color: #0d1e50;
}
.back-btn {
  background: #f84e77;
  color: #ffffff;
  padding: 12px 28px 11px;
  display: inline-block;
  font-weight: 500;
  line-height: 1;
  border: 2px solid #f84e77;
  transition: all 0.5s ease 0s;
}
.back-btn.white:hover {
  color: #ffffff;
  border-color: #2cbbf3;
  background: #13c2f7c7;
}
/* get started free end */
.serv-content .title {
  margin-bottom: 9px;
  font-size: 24px;
}
.serv-item .title a{
  color: rgb(11, 141, 248);
}
.serv-item{
  padding: 20px;
}
.serv-btn a{
  color: rgb(255, 255, 255);
  background-color: #01a3e4;
  padding: 10px;
  transition: all 0.4s ease-in-out;
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  -ms-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
}
.serv-overlay .serv-btn a:hover{
  color: rgb(12, 12, 12);
  letter-spacing: 1px;
}
.quality-serv{
  margin-bottom: 25px;
  margin-right: 40px;
  padding: 20px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px -1px, rgba(0, 0, 0, 0.06) 0px 2px 4px -1px;

}
.quality-icon{
  padding-top: 1.5rem;
  padding-right: 25px;
}
.quality-icon svg path{
  fill: #2183ec;
}

.image-part {
  position: relative;
}
.serv-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  opacity: 0;
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
}
.serv-content:hover .serv-overlay{
  position: absolute;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(92, 203, 255, 0.5);
  opacity: 1;
}

.serv-btn {
  position: absolute;
  top: 50%;
  right: 0;
  left: 50%;
  bottom: 0%;
  transform: translate(-50%, -50%);
  content: "";
}
/*  */
/*button*/
.service-div-btn {
  display: flex;
  place-items: center;
  align-items: center;
  justify-content: center;
}

.service-div-btn a {
  text-decoration: none;
  font-size: 1rem;
  padding: 8px 1rem;
  border-radius: 3px;
  color: #fff;
  background-color: #0eafec;
  position: relative;
  overflow: hidden !important;
  z-index: 1;
  transition: all 0.4s linear;
  -webkit-transition: all 0.4s linear;
  -moz-transition: all 0.4s linear;
  -ms-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
}

.service-div-btn a::before {
  content: '';
  position: absolute;
  background-color: rgba(250, 248, 248, 0.616);
  color: rgb(0, 0, 0);
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  transform: translateX(-100%);
  transition: all 0.3s linear
}

.service-div-btn a:hover:before {
  transform: translateX(0%);
  z-index: -1
}

.service-div-btn a:hover {
  color: rgb(0 0 0);
  border-radius: 3px;
  background-color: #f7f7f76b;
}

/* -----Contact us ------ */
.contact {
  border-radius: 20px;
  padding: 30px;
  position: relative;
  box-shadow: rgb(50 50 93 / 25%) 0px 50px 100px -20px, rgb(0 0 0 / 30%) 0px 30px 60px -30px;
  background-color: #4bc7f9;
  border: 5px solid #fff;
}

.left {
    border-radius: 20px;
    padding: 20px;
    background-color: #fff;
    box-shadow: rgb(17 17 26 / 10%) 0px 1px 0px, rgb(17 17 26 / 10%) 0px 8px 24px, rgb(17 17 26 / 10%) 0px 16px 48px;
    color: #fff;
}

.right_heading h4{
  font-size: 30px;
  letter-spacing: 1px;
  color: #00b4db;
  margin-top: 10px;
  font-weight: 700;
}

.para {
  font-size: 19px;
  letter-spacing: 2px;
  margin-bottom: 30px;
  color: #fff;
}

input {
  background-color: transparent;
  outline: none;
  border: none;
  resize: none;
  border-bottom: 2px solid #fff;
  margin-bottom: 30px;
  width: 100%;
  min-height: 30px;
}

textarea {
  background-color: transparent;
  outline: none;
  border: none;
  resize: none;
  border-bottom: 2px solid #fff;
  margin-bottom: 20px;
  width: 100%;
}

.label {
  font-size: 16px;
  color: #fff;
}

.contact-btn{
  position: relative;
  cursor: pointer;
  display: inline-block;
  white-space: nowrap;
  background: linear-gradient(180deg, #00b4db 0%, #6dd5ed 100%);
  border: 0.5px solid rgba(255, 255, 255, 0.8);
  border-width: 0.5px 0.5px 0.5px 0.5px;
  padding: 10px 30px;
  box-shadow: rgba(17, 17, 26, 0.1) 0px 4px 16px,
  rgba(17, 17, 26, 0.05) 0px 8px 32px;
  color: #fff;
  font-size: 16px;
}

.reset-btn{
  background-color: transparent;
}

.contact-btn:hover {
  box-shadow: rgb(204, 219, 232) 3px 3px 6px 0px inset,
    rgba(255, 255, 255, 0.5) -3px -3px 6px 1px inset;
}

.tabs {
  padding: 30px 20px 0;
}

.tab {
  font-size: 20px;
  padding: 20px 30px;
  margin-bottom: 30px;
  border: 1px solid #fff;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 18px 50px -10px;
  background-color: #4bc7f9;
}

.tab i {
  padding-right: 20px;
  color: #5d26c1;
}



.blog_details .span_item ul{
  padding-top: 20px;
}

.blog_details .span_item h4{
  font-size: 24px;
}

.reply_item form input{
  margin-bottom: 0px;
}
.reply_item form textarea{
  margin-bottom: 10px;
}

@media only screen and (max-width: 950px) {

  .tabs {
    padding: 30px 0 0 0;
  }

  .tab {
    padding: 20px 10px;
    margin-bottom: 50px;
    font-size: 18px;
  }

  .tab i {
    padding-right: 10px;
    font-size: 16px;
  }
}

/* ------Contact us end------ */

/*========================================

 ========index page - start

=========================================*/

.indexlink{
  background: #f26522;
  color: #fff !important;
}
.image-container span img {
    height: auto;
    max-width: 50%;
}
.features-item{
  background-color: #eeeeeea6;
  padding: 30px;
}
.features-item .title-text{
  font-size: 18px;
  margin-top: 16px;
}
#preview-section .section-tittle::before{
  content: '';
  position: absolute;
  width: 80px;
  height: 2px;
  background-color: #f26522;
}
.image-container a img{
  position: relative;
}
.overlay-preview{
  position: absolute;
  top: 0;
  left: 15px;
  right: 15px;
  bottom: 0;
  background-color: rgba(75,199,249,0.5);
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
}
.overlay-preview:hover{
  background-color: rgba(75,199,249,0.8);
  cursor: pointer;
}
.overlay-preview .title-text{
  color: #fff !important;
  align-items: center;
  font-size: 20px;
  transform: translateY(231px);
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
} 
.overlay-preview:hover .title-text{
  font-size: 26px;
  font-weight: 700;
}
.banner-content-preview{
   transform: translateY(500px);
}
.preview-h1{
  color: #fff;
  background: none !important;
}
.preview-item-img{
  height: 60vh;
}
.preview-desc p{
  color: #fff;
  margin-bottom: 30px;
}
.preview-button{
  background-color: #ffffff;
  padding: 10px 15px;
  display: inline-block;
  border-radius: 50px;
}
.preview-button a{
  color: #4bc7f9;
}
.preview-button a i{
  margin-left: 5px;
  color: #4bc7f9;
}
#preview-banner-section{
  background-color: #4bc7f9;
}
.preview-menu-sticky {
  background: #ffffff;
  border-bottom: 1px solid #4bc7f9;
}

.preview-content{
  padding-top: 130px;
}
.indexmenusticky{
  border-bottom: 1px solid #f1f1f11c;
}
.preview-item .image-container img{
  width: 350px;
  height: auto;
}

.register_section {
    background-image: url(../image/re_banner5.png);
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
    background-position: center;
}
.register_section {
    background-color: #4bc7f9;
}
.register_content h2 {
    color: #ffffff;
    font-size: 48px;
    margin-bottom: 30px;
}
.register_content h6 {
    font-size: 41px;
    font-weight: 700;
    line-height: 48px;
    color: #efefef;
}
.indexmenusticky .navbar-collapse{
  justify-content: center;
}
.indexmenusticky .navbar-nav .nav-item .nav-link{
  padding: 10px 10px;
  color: #4bc7f9;
}
.footer-bottom{
   padding: 20px 0px;
}

