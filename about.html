<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Cropo-King / About</title>
	
	<meta name="description" content="Cropking is a Corporate business Html5 Template. It created for professionals.Bootstrap 4 Exclusive Features made it unique and extremely useful for any professionals users. Cropking is fully responsive and supported in to all device & browsers.">
    <meta name="keywords" content="Business Solutions, Blog, Marketing, Finance, Time, Creative, Design, Office">
    <meta name="author" content="bitspeck">

  <!-- favicon -->
  <link rel="shortcut icon" type="image/Fav-icon-corp-king.png" href="image/Fav-icon-corp-king.png">
  <!-- animate.min - css -->
  <link rel="stylesheet" href="css/animate.min.css">
  <!-- animate - css -->
  <link rel="stylesheet" href="css/animate.css">
  <!-- owl.carousel.min - css -->
  <link rel="stylesheet" href="css/owl.carousel.min.css">
  <!-- owl.theme.default.min - css -->
  <link rel="stylesheet" href="css/owl.theme.default.min.css">
  <!-- font-awesome.min - css -->
  <link rel="stylesheet" href="css/font-awesome.min.css">
  <!-- bootstrap.min - css -->
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <!-- silk -->
  <link rel="stylesheet" href="css/slick.css">
  <link rel="stylesheet" href="css/slick-theme.css">
  <!-- main - css -->
  <link rel="stylesheet" href="css/style.css">
  <!-- responsive - css -->
  <link rel="stylesheet" href="css/responsive.css">


</head>
<body>
    <!-- preloder -->
	<div id="preloader"></div>
	<!-- preloder end -->
	<header class="header sticky-header clearfix">
        <div class="menu-sticky">
			<div class="bottom-bar d-block">
				<div class="top-info">
					<!-- mobile menu -->

						 
					<!-- mobile menu end -->
					<div class="container">
						<ul class="personal-info">
							<li>
								<p><svg width="11" height="16" viewBox="0 0 11 16" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M9.81077 0H1.96102C1.30705 0 0.77179 0.485943 0.77179 1.08069V14.9189C0.77179 15.5136 1.30705 16 1.96102 16H9.81077C10.4647 16 11 15.5145 11 14.9193V1.08069C11 0.485943 10.4647 0 9.81077 0ZM4.63026 0.778971H7.14153C7.22102 0.778971 7.28541 0.837486 7.28541 0.910171C7.28541 0.9824 7.22102 1.04091 7.14153 1.04091H4.63026C4.55077 1.04091 4.48638 0.9824 4.48638 0.910171C4.48638 0.837486 4.55077 0.778971 4.63026 0.778971ZM5.88589 15.4597C5.5574 15.4597 5.29128 15.2178 5.29128 14.9189C5.29128 14.6199 5.5574 14.3785 5.88589 14.3785C6.21439 14.3785 6.48051 14.6199 6.48051 14.9189C6.48051 15.2178 6.21439 15.4597 5.88589 15.4597ZM10.1725 14H1.59932V1.71383H10.1725V14Z" fill="black"/>
								</svg>
								 ****** 456 7890 </p>
							</li>
							<li>
								<p><svg width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M14.0251 6.15735C14.0251 2.75674 11.2683 0 7.86773 0C4.46712 0 1.71038 2.75674 1.71038 6.15735C0.831243 6.26682 0 6.84151 0 8.1756V11.3911C0 12.8962 1.05359 13.4436 2.05245 13.4436H3.04447C3.23338 13.4436 3.38654 13.2904 3.38654 13.1015V6.47891C3.38654 6.28999 3.23338 6.13683 3.04447 6.13683H2.39453C2.39453 3.11408 4.84498 0.663626 7.86773 0.663626C10.8905 0.663626 13.3409 3.11408 13.3409 6.13683H12.691C12.5021 6.13683 12.3489 6.28999 12.3489 6.47891V13.0912C12.3489 13.2801 12.5021 13.4333 12.691 13.4333H13.3409C13.2452 15.7834 12.1163 16.3341 9.86203 16.4333V16.2041C9.86203 15.8262 9.55574 15.52 9.17788 15.52H7.02623C6.64836 15.52 6.34208 15.8262 6.34208 16.2041V17.3158C6.34208 17.6937 6.64836 18 7.02623 18H9.18472C9.56258 18 9.86887 17.6937 9.86887 17.3158V17.1174C12.0376 17.0182 13.9122 16.5701 14.0251 13.4128C14.9042 13.3033 15.7355 12.7286 15.7355 11.3945V8.17902C15.7355 6.83808 14.9076 6.26682 14.0251 6.15735ZM2.70239 6.79704V12.7491H2.03535C1.52908 12.7491 0.667047 12.5713 0.667047 11.3808V8.16534C0.667047 6.98518 1.51881 6.79704 2.03535 6.79704H2.70239V6.79704ZM9.18472 16.7993V17.3193H7.02623V16.2075H9.18472V16.7993ZM15.0513 11.3911C15.0513 12.5713 14.1995 12.7594 13.683 12.7594H13.0331V6.82098H13.7001C14.2064 6.82098 15.0684 6.99886 15.0684 8.18928L15.0513 11.3911Z" fill="black"/>
									</svg>
									 Hi! Here comes custom txt line </p>
							</li>
							<li>
								<p><svg width="17" height="12" viewBox="0 0 17 12" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M17 0.252088C17 0.249489 17 0.247179 16.9993 0.24458C16.9986 0.241981 16.9983 0.235484 16.998 0.231008C16.9964 0.210962 16.9924 0.191184 16.9861 0.172101C16.9852 0.169502 16.9861 0.166759 16.9845 0.16416C16.9829 0.161561 16.9831 0.161994 16.9826 0.160839C16.9745 0.139866 16.9635 0.1201 16.9501 0.102077C16.9471 0.0980341 16.9439 0.0944246 16.9406 0.0905264C16.9276 0.0746072 16.9126 0.0603522 16.8961 0.0480786C16.8944 0.0467792 16.893 0.045191 16.8913 0.0440359C16.8728 0.0318149 16.8528 0.0221054 16.8319 0.0151601C16.827 0.0134276 16.8221 0.0119835 16.8175 0.0105397C16.7954 0.00387172 16.7726 0.000324524 16.7495 0H0.250489C0.227446 0.000324524 0.20456 0.00387172 0.182487 0.0105397C0.177466 0.0119835 0.172732 0.0134276 0.168141 0.0151601C0.147163 0.0221054 0.127191 0.0318149 0.108746 0.0440359C0.107025 0.045191 0.10559 0.0467792 0.103868 0.0480786C0.0873814 0.0603522 0.0724461 0.0746072 0.0593944 0.0905264C0.0562382 0.0944246 0.0529385 0.0980341 0.0499257 0.102077C0.0364923 0.1201 0.0255379 0.139866 0.0173592 0.160839C0.0173592 0.161994 0.0159246 0.163005 0.0154942 0.16416C0.0150638 0.165315 0.0147768 0.169502 0.013916 0.172101C0.00761029 0.191184 0.00361233 0.210962 0.0020085 0.231008C0.0020085 0.235628 0.000717272 0.240104 0.000717272 0.24458C0.000717272 0.249056 0 0.249489 0 0.252088V11.7469C0 11.7502 0.000860784 11.7534 0.00100425 11.7567C0.00142282 11.7658 0.00233278 11.7749 0.00373009 11.7839C0.00487781 11.7912 0.00588205 11.7983 0.00760363 11.8058C0.00981801 11.8146 0.0124999 11.8232 0.0156376 11.8317C0.0180765 11.8384 0.0203719 11.8452 0.0233847 11.8517C0.0273631 11.8602 0.0318177 11.8685 0.036727 11.8766C0.0401701 11.8823 0.0436133 11.8883 0.0474868 11.8937C0.0530491 11.9014 0.0590363 11.9087 0.0654199 11.9157C0.0701542 11.9209 0.0748886 11.9261 0.0797664 11.931C0.0827791 11.9337 0.0850746 11.9371 0.0882308 11.9398C0.091387 11.9425 0.0933955 11.9422 0.095691 11.9441C0.115277 11.96 0.13714 11.9728 0.160537 11.9821C0.165558 11.9841 0.170723 11.9853 0.175888 11.9869C0.19852 11.9943 0.222098 11.9984 0.245899 11.9991C0.24762 11.9991 0.249198 12 0.25092 12H0.25422H16.7458H16.7491C16.7508 12 16.7524 11.9991 16.754 11.9991C16.7778 11.9985 16.8014 11.9943 16.8241 11.9869C16.8291 11.9853 16.8342 11.9841 16.8385 11.9821C16.8619 11.9728 16.8838 11.96 16.9034 11.9441C16.9057 11.9422 16.9086 11.9417 16.9109 11.9398C16.9132 11.9379 16.9164 11.9337 16.9194 11.931C16.9245 11.9261 16.9293 11.921 16.9337 11.9157C16.9401 11.9087 16.9461 11.9014 16.9517 11.8937C16.9555 11.8883 16.959 11.8825 16.9624 11.8766C16.9673 11.8685 16.9718 11.8602 16.9758 11.8517C16.9788 11.8452 16.9811 11.8384 16.9835 11.8317C16.9866 11.8232 16.9893 11.8146 16.9915 11.8058C16.9933 11.7986 16.9943 11.7914 16.9954 11.7839C16.9968 11.7749 16.9977 11.7658 16.9981 11.7567C16.9981 11.7534 16.9991 11.7502 16.9991 11.7469L17 0.252088ZM5.88865 5.46767L8.33588 7.58674C8.38145 7.62616 8.43955 7.64784 8.49964 7.64784C8.55973 7.64784 8.61784 7.62616 8.66341 7.58674L11.1108 5.46767L16.2055 11.4942H0.794077L5.88865 5.46767ZM0.501553 11.0604V0.802897L5.50847 5.13805L0.501553 11.0604ZM11.491 5.13805L16.4979 0.802897V11.0604L11.491 5.13805ZM16.0719 0.504753L10.9782 4.91571C10.9773 4.91643 10.9762 4.91686 10.9753 4.91773C10.9745 4.91859 10.9736 4.91975 10.9726 4.92062L8.49971 7.06192L6.02681 4.92062C6.02581 4.91975 6.02509 4.91859 6.02408 4.91773L6.02122 4.91571L0.927213 0.504753H16.0719Z" fill="black"/>
									</svg>																											
									<EMAIL> </p>
							</li>
						</ul>
						<!-- Right Sec -->
						<div class="right-sec float-right"> 
							<!-- Language -->
							<select class="selectpicker language-select bs-select-hidden">
							<option>English</option>
							<option>French</option>
							<option>Spanish</option>
							</select>
							<div class="btn-group bootstrap-select">
								<!-- social -->
								<ul class="social">
									<li><a href="#."><svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
										<g clip-path="url(#clip0)">
										<path d="M7.22674 18.5214C6.13451 18.4656 6.15508 16.9293 7.22669 16.876C10.2622 16.8865 18.2687 16.8788 21.4008 16.881C22.2575 16.881 22.9904 16.298 23.1831 15.4634L24.9616 7.76199C25.0634 7.32103 24.9606 6.86464 24.6796 6.50991C24.3986 6.15518 23.9779 5.94887 23.5254 5.94887C22.2654 5.94887 11.57 5.90045 6.09186 5.87862L5.22348 2.65654C5.01502 1.88655 4.31235 1.34882 3.51466 1.34882H0.732811C0.328104 1.34882 0 1.67692 0 2.08163C0 2.48633 0.328104 2.81444 0.732811 2.81444H3.51466C3.65199 2.81444 3.7729 2.90702 3.80862 3.03878L7.14364 15.4123C6.55724 15.4336 6.01105 15.6722 5.59687 16.0924C5.16168 16.534 4.92796 17.1186 4.93876 17.7385C4.96035 18.9783 5.98673 19.9869 7.22674 19.9869H8.29528C8.13645 20.3203 8.04729 20.693 8.04729 21.0862C8.04729 22.5005 9.1979 23.6511 10.6122 23.6511C12.0266 23.6511 13.1772 22.5005 13.1772 21.0862C13.1772 20.693 13.088 20.3203 12.9291 19.9869H17.5564C17.3977 20.3202 17.3086 20.6927 17.3086 21.0858C17.3086 22.5001 18.4592 23.6508 19.8735 23.6508C21.2878 23.6508 22.4385 22.5001 22.4385 21.0858C22.4385 20.6774 22.342 20.2913 22.1715 19.9483C22.4615 19.8506 22.6706 19.5771 22.6706 19.2542C22.6706 18.8495 22.3425 18.5214 21.9378 18.5214H7.22674ZM11.7115 21.0863C11.7115 21.6924 11.2184 22.1856 10.6122 22.1856C10.006 22.1856 9.51287 21.6924 9.51287 21.0863C9.51287 20.4804 10.0055 19.9875 10.6113 19.987H10.6131C11.2189 19.9875 11.7115 20.4804 11.7115 21.0863ZM19.8735 22.1852C19.2674 22.1852 18.7742 21.692 18.7742 21.0859C18.7742 20.4857 19.2578 19.9966 19.8557 19.987H19.8914C20.4894 19.9966 20.973 20.4857 20.973 21.0859C20.9729 21.692 20.4797 22.1852 19.8735 22.1852V22.1852ZM23.5308 7.41987C23.5351 7.42534 23.5345 7.42793 23.5335 7.43213L22.7842 10.6773H20.2466L20.6787 7.40291L23.5195 7.41434C23.5239 7.41444 23.5265 7.41444 23.5308 7.41987V7.41987ZM15.9757 15.4153V12.1429H18.5749L18.143 15.4153H15.9757ZM12.3419 15.4153L11.9065 12.1429H14.5101V15.4153H12.3419ZM8.96834 15.4153H8.96604C8.78743 15.4153 8.63007 15.2948 8.58361 15.1232L7.78035 12.1429H10.4279L10.8633 15.4153H8.96834ZM11.7114 10.6773L11.2707 7.36505L14.5101 7.37809V10.6773H11.7114ZM15.9757 10.6773V7.38401L19.2012 7.397L18.7683 10.6773H15.9757ZM9.79138 7.35904L10.2329 10.6773H7.38532L6.48738 7.3457L9.79138 7.35904ZM21.4008 15.4153H19.6213L20.0531 12.1429H22.4457L21.755 15.1336C21.7167 15.2995 21.5711 15.4153 21.4008 15.4153Z" fill="black"/>
										</g>
										<defs>
										<clipPath id="clip0">
										<rect width="25" height="25" fill="white"/>
										</clipPath>
										</defs>
										</svg>
										</a>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
				<nav id="navbar_top" class="navbar navbar-bg-color navbar-expand-lg text-white navbar-light shadow-sm">
					<div class="container"> 
						<a class="navbar-brand d-flex align-items-center" href="index.html">
						<img class="w-50" src="image/logo-corp-king.png" alt="logo">
						</a> 
						<button class="navbar-toggler toggler-nav navbar-toggler-right border-0" style="background-color: #fff; " type="button" data-toggle="collapse" data-target="#navbar4">
							<span class="navbar-toggler-icon"></span>
							<span class="navbar-toggler-icon"></span>
							<span class="navbar-toggler-icon"></span>
						</button>
						<div class="collapse navbar-collapse" id="navbar4">
							<ul class="navbar-links">
								<li class="navbar-dropdown">
								<a href="index.html">Home</a>
								</li>
								<li class="navbar-dropdown">
								<a href="about.html">About</a>
								
								</li>
								<li class="navbar-dropdown">
								<a href="#">Service</a>
								<div class="dropdown">
									<a href="service.html">Service</a>
									<a href="service-details.html">Service Details</a>
								</div>
								</li>
								<li class="navbar-dropdown">
								<a href="team.html">Team</a>
								</li>
								<li class="navbar-dropdown">
								<a href="#">Blog</a>
								<div class="dropdown">
									<a href="blog.html">Blog</a>
									<a href="blog-details.html">Blog Details</a>
								</div>
								</li>
								<li class="navbar-dropdown">
									<a href="contact.html">Contact</a>
								</li>
							</ul>
							<ul class="navbar-nav ml-auto mt-3 mt-lg-0">
								<li class="nav-item nav-icons"> 
									<a class="nav-link" href="#">
										<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path d="M10.0001 0.041626C4.47724 0.041626 0 4.51886 0 10.0417C0 14.995 3.60522 19.0972 8.33225 19.8916V12.128H5.91994V9.33417H8.33225V7.27413C8.33225 4.88389 9.79213 3.58135 11.9247 3.58135C12.9461 3.58135 13.8238 3.65746 14.0786 3.69098V6.18942L12.5995 6.19013C11.44 6.19013 11.2165 6.74102 11.2165 7.54969V9.33274H13.9832L13.6223 12.1265H11.2165V19.9584C16.1642 19.3562 20 15.1495 20 10.0388C20 4.51886 15.5228 0.041626 10.0001 0.041626Z" fill="#010002"/>
										</svg><span class="d-lg-none ml-3">Facebook</span>
									</a> 
								</li>
								<li class="nav-item nav-icons"> 
									<a class="nav-link" href="#">
										<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path d="M10 0C4.478 0 0 4.478 0 10C0 15.522 4.478 20 10 20C15.522 20 20 15.522 20 10C20 4.478 15.522 0 10 0ZM14.5659 7.79694C14.5703 7.89536 14.5724 7.99423 14.5724 8.09357C14.5724 11.1266 12.2638 14.624 8.04184 14.6242H8.04199H8.04184C6.74561 14.6242 5.5394 14.2442 4.52362 13.5931C4.70322 13.6143 4.88602 13.6249 5.07111 13.6249C6.14655 13.6249 7.13623 13.2581 7.92191 12.6424C6.91711 12.6237 6.06995 11.9601 5.77759 11.0481C5.91751 11.075 6.0614 11.0896 6.20895 11.0896C6.41846 11.0896 6.6214 11.0614 6.81427 11.0088C5.76401 10.7985 4.97284 9.8703 4.97284 8.75885C4.97284 8.74847 4.97284 8.73917 4.97314 8.72955C5.28244 8.90152 5.63614 9.00497 6.01273 9.01657C5.39642 8.60535 4.9913 7.90237 4.9913 7.10602C4.9913 6.68549 5.10498 6.2915 5.30212 5.9523C6.43402 7.34116 8.12561 8.25455 10.0333 8.35052C9.9939 8.18237 9.9736 8.0072 9.9736 7.82715C9.9736 6.56006 11.0016 5.53207 12.2691 5.53207C12.9294 5.53207 13.5257 5.81116 13.9445 6.25732C14.4675 6.15417 14.9585 5.96313 15.4021 5.70023C15.2304 6.23596 14.8666 6.68549 14.3927 6.96976C14.857 6.91422 15.2995 6.79108 15.7108 6.60828C15.4036 7.06863 15.014 7.47299 14.5659 7.79694Z" fill="black"/>
										</svg>
										<span class="d-lg-none ml-3">Twitter</span>
									</a> 
								</li>
								<li class="nav-item nav-icons"> 
									<a class="nav-link" href="#">
										<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path d="M10 0C4.478 0 0 4.478 0 10C0 15.522 4.478 20 10 20C15.522 20 20 15.522 20 10C20 4.478 15.522 0 10 0ZM7.09412 15.1172H4.65866V7.79007H7.09412V15.1172ZM5.87646 6.78955H5.8606C5.04333 6.78955 4.51477 6.22696 4.51477 5.52383C4.51477 4.80484 5.05951 4.25781 5.89264 4.25781C6.72577 4.25781 7.23846 4.80484 7.25433 5.52383C7.25433 6.22696 6.72577 6.78955 5.87646 6.78955ZM15.8759 15.1172H13.4407V11.1974C13.4407 10.2122 13.0881 9.54041 12.2069 9.54041C11.5341 9.54041 11.1334 9.99359 10.9573 10.4311C10.8929 10.5876 10.8772 10.8064 10.8772 11.0254V15.1172H8.44193C8.44193 15.1172 8.47382 8.47748 8.44193 7.79007H10.8772V8.82751C11.2009 8.32825 11.7799 7.6181 13.0721 7.6181C14.6744 7.6181 15.8759 8.66531 15.8759 10.9158V15.1172Z" fill="black"/>
										</svg>
										<span class="d-lg-none ml-3">linkedin</span>
									</a> 
								</li>
								<li class="nav-item nav-icons"> 
									<a class="nav-link" href="#">
										<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path d="M10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20ZM13.5692 9.28583H15.0008V7.855H16.4325V9.28667H17.8517V10.7183H16.4325V12.15H15.0008V10.7183H13.5692V9.28583ZM7.14917 4.99917C8.41083 4.99917 9.5725 5.44333 10.4967 6.30167L9.14167 7.61667C8.60917 7.09583 7.88 6.84667 7.14917 6.84667C5.41917 6.84667 4.03583 8.27833 4.03583 9.99583C4.03583 11.7133 5.415 13.145 7.14917 13.145C8.46333 13.145 9.76583 12.375 9.97583 10.9958H7.14917V9.27833H11.8633C11.9158 9.5525 11.94 9.82667 11.94 10.1125C11.94 12.9675 10.0242 14.9992 7.14917 14.9992V15C4.375 15 2.14833 12.7617 2.14833 9.99917C2.14833 7.23667 4.375 4.99917 7.14917 4.99917Z" fill="black"/>
										</svg>
										<span class="d-lg-none ml-3">Linkedin</span>
									</a> 
								</li>
							</ul>	
						</div>
					</div>
				</nav>
				<!-- toggler navbar -->
				
	<!--The html below this line is for display purpose only-->
        	</div>
		</div>
    </header>  
    <!-- page   breadcrumb -->

   <section id="page-breadcrumb">
    	<div class="container">
    		<div class="common-overlay"></div>
    		<div class="breadcrumb-menu ">
            <h3>About Us</h3>
            <ul class="steps breadcrumb d-flex">
                <li class="list1">
                  <a href="index.html">
                    Home 
                  </a>
                </li>
                <li>
                    <svg width="18" height="27" viewBox="0 0 18 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14.7754 0.827134L0.26011 24.3416C-0.245635 25.1606 0.00866776 26.2335 0.827345 26.7386C1.11239 26.9145 1.42819 26.9988 1.74064 26.9988C2.3248 26.9988 2.89561 26.7054 3.22475 26.1713L17.74 2.65659C18.2455 1.83791 17.9914 0.764928 17.1725 0.259898C16.3541 -0.24537 15.2802 0.00845604 14.7754 0.827134Z" fill="black"/>
                    </svg>    
                </li>
                <li class="active">
                  <a>
                    About Us
                  </a>
                </li>
              </ul>
            </div>
    	</div>
    </section>
    <!--  end breadcrumb -->

    <!-- about details -->
    <section id="bit-about-details">
        <div class="" id="venue">
            <div class="container">
                <div class="row animate-in-down">
                    <div class="p-0 col-md-6">
                        <div class="main-platform">
                            <div class="section-tittle common-title text-left">
                                <h2 class="wow fadeInDown" data-wow-duration="2s" data-wow-delay=".2s">
                                Why Our Service is Better!</h2>
                            </div>
                            <div class="carousel slide platform-slide" data-ride="carousel" id="carousel1">
                                <div class="carousel-inner" role="listbox">
                                    <div class="carousel-item"> 
                                        <div class="bit-tittle-text">
                                            <div class="owl-item inner-item active">
                                                <h4 class="inner-heading">A Place Can be Better in Your Job</h4>
                                                <p class="mb-3">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Recusandae saepe id nesciunt, illum animi ducimus sed obcaecati culpa.</p>
                                                <p class="mb-3">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Recusandae saepe id nesciunt, illum animi ducimus sed obcaecati culpa.</p>
                                                <p class="mb-4">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Recusandae saepe id nesciunt, illum animi ducimus sed obcaecati culpa.</p>
                                                <!----><!---->
                                                <div class="btn-slide">
                                                    <a href="service.html">View Details</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="carousel-item active"> 
                                        <div class="bit-tittle-text">
                                            <div class="owl-item ng-tns-c18-1 ng-trigger ng-trigger-autoHeight ng-star-inserted active">
                                                <h4 class="inner-heading">This Place Why You Choose?</h4>
                                                <p class="mb-3">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Recusandae saepe id nesciunt, illum animi ducimus sed obcaecati culpa. Architecto aliquid laboriosam porro laudantium totam accusamus non quasi cumque! Vitae, inventore.</p>
                                                <p class="mb-3">Lorem ipsum dolor sit amet consectetur adipisicing elit. Nisi ducimus hic provident voluptate non deleniti modi optio quis culpa suscipit corrupti assumenda porro quo quasi, natus deserunt? Ullam, dicta mollitia.</p>
                                                <p class="mb-4">Lorem ipsum dolor, sit amet, consectetur adipisicing elit. Sed accusantium eius quo laudantium excepturi culpa.</p>
                                                <!----><!---->
                                                <div class="btn-slide">
                                                    <a href="service.html">View Details</a>
                                                </div>
                                            </div>  
                                        </div>
                                        
                                    </div>
                                    <div class="carousel-item"> 
                                        <div class="bit-tittle-text">
                                            <div class="owl-item ng-tns-c18-1 ng-trigger ng-trigger-autoHeight ng-star-inserted active">
                                                <h4 class="inner-heading">A Place can be better in this platform</h4>
                                                <p class="mb-3">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Recusandae saepe id nesciunt, illum animi ducimus sed obcaecati culpa. Architecto aliquid laboriosam porro laudantium totam accusamus non quasi cumque! Vitae, inventore.</p>
                                                <p class="mb-3">Lorem ipsum dolor sit amet consectetur adipisicing elit. Nisi ducimus hic provident voluptate non deleniti modi optio quis culpa suscipit corrupti assumenda porro quo quasi, natus deserunt? Ullam, dicta mollitia.</p>
                                                <p class="mb-4">Lorem ipsum dolor, sit amet, consectetur adipisicing elit. Sed accusantium eius quo laudantium excepturi culpa.</p>
                                                <!----><!---->
                                                <div class="btn-slide">
                                                    <a href="service.html">View More</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div> 
                            </div>
                        </div>
                    </div>
                    <div class="p-4 col-md-6 align-self-center text-color">
                        <ul>
                            <li class="d-flex">
                                <div class="icon others-icon">
                                    <svg width="60" height="70" viewBox="0 0 60 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M59.5886 9.87102C59.4305 8.32145 58.1334 7.11938 56.5714 7.07485C43.4324 6.7003 34.7327 2.31193 31.6022 0.442579C30.6138 -0.147663 29.3858 -0.14739 28.3978 0.442579C26.5411 1.55135 22.5484 3.64308 16.6666 5.19539C16.1204 5.33964 15.7945 5.89928 15.9386 6.4454C16.0827 6.99153 16.6427 7.31718 17.1886 7.17334C23.3132 5.55683 27.4976 3.36278 29.4467 2.19896C29.7881 1.99489 30.2121 1.99489 30.5536 2.19896C33.8217 4.15041 42.8933 8.73152 56.5132 9.11973C57.0521 9.13503 57.4993 9.54728 57.5535 10.0785C58.0065 14.5187 58.7171 26.1354 55.5416 38.0838C53.7602 44.7866 51.0152 50.5825 47.3831 55.3104C43.0123 60.9997 37.2928 65.2094 30.3834 67.8226C30.1361 67.916 29.864 67.916 29.6165 67.8226C22.7074 65.2094 16.9878 60.9997 12.6171 55.3104C8.98494 50.5825 6.23999 44.7866 4.45847 38.0838C1.28283 26.1354 1.99355 14.5184 2.44651 10.0785C2.50074 9.54728 2.94796 9.13503 3.48698 9.11973C6.51974 9.03326 9.5368 8.72824 12.4545 8.21285C13.0108 8.11464 13.382 7.58395 13.2838 7.02759C13.1856 6.47136 12.6551 6.09981 12.0987 6.1983C9.27903 6.69634 6.36197 6.99125 3.42879 7.07485C1.86679 7.11938 0.569647 8.32145 0.411465 9.87102C-0.0528331 14.4228 -0.780766 26.3348 2.48162 38.6093C4.33294 45.5748 7.19714 51.6132 10.995 56.5567C15.601 62.5522 21.6228 66.9864 28.8927 69.7359C29.2497 69.8711 29.625 69.9386 30.0001 69.9386C30.3752 69.9386 30.7503 69.8711 31.1072 69.7359C38.3774 66.9864 44.3992 62.5522 49.0052 56.5567C52.8029 51.6132 55.6672 45.5748 57.5185 38.6093C60.7807 26.3348 60.0529 14.4228 59.5886 9.87102V9.87102Z" fill="black"/>
                                        <path d="M6.27319 14.3814C5.97977 17.2585 5.23121 27.4582 7.92248 37.9087C9.40102 43.6497 11.688 48.6158 14.7196 52.6695C18.4076 57.6005 23.2275 61.2164 29.0458 63.417C29.3534 63.5332 29.6766 63.5914 30 63.5914C30.3231 63.5914 30.6466 63.5332 30.9542 63.4168C36.7722 61.2164 41.5923 57.6006 45.2803 52.6695C48.312 48.6158 50.5989 43.6498 52.0774 37.9087C54.7687 27.4582 54.0201 17.2585 53.7267 14.3814C53.5904 13.0453 52.4722 12.0089 51.1257 11.9705C40.7314 11.6743 33.855 8.20588 31.3812 6.72871C30.5293 6.21974 29.4708 6.21988 28.6188 6.72857C26.1449 8.20588 19.2685 11.6743 8.87416 11.9705C7.52771 12.0089 6.40951 13.0453 6.27319 14.3814V14.3814ZM29.6677 8.48482C29.7702 8.42362 29.8851 8.39302 30 8.39302C30.1148 8.39302 30.2297 8.42362 30.3323 8.48495C32.9438 10.0445 40.1923 13.7053 51.0674 14.0154C51.3909 14.0246 51.6592 14.2711 51.6915 14.5889C51.9763 17.3807 52.703 27.2765 50.0963 37.3984C46.9551 49.5957 40.2713 57.7058 30.2307 61.5032C30.082 61.5598 29.9181 61.5596 29.7694 61.5034C19.7286 57.7057 13.0448 49.5957 9.90357 37.3984C7.29686 27.2767 8.02356 17.3808 8.30837 14.5889C8.34074 14.2713 8.60902 14.0246 8.93263 14.0154C19.8076 13.7053 27.0561 10.0445 29.6677 8.48482V8.48482Z" fill="black"/>
                                        <path d="M29.9999 48.0023C34.6666 48.0023 39.0635 45.7249 41.7619 41.9104C42.0881 41.4492 41.9787 40.811 41.5175 40.4847C41.0565 40.1584 40.418 40.2678 40.0918 40.7291C37.7764 44.0024 34.0036 45.9567 29.9999 45.9567C23.1887 45.9567 17.6475 40.4154 17.6475 33.6042C17.6475 26.793 23.1887 21.2517 29.9999 21.2517C36.8112 21.2517 42.3524 26.793 42.3524 33.6042C42.3524 34.6694 42.2169 35.7265 41.9494 36.7464C41.8062 37.2928 42.133 37.8519 42.6794 37.9952C43.2257 38.1379 43.7849 37.8116 43.9282 37.2652C44.2399 36.076 44.398 34.8444 44.398 33.6042C44.398 25.6651 37.939 19.206 29.9998 19.206C22.0606 19.206 15.6016 25.665 15.6016 33.6042C15.6016 41.5435 22.0608 48.0023 29.9999 48.0023Z" fill="black"/>
                                        <path d="M35.3644 26.4217C34.5103 26.4559 33.7206 26.8206 33.1409 27.4485L28.0038 33.0123L26.7318 31.7831C25.4631 30.5571 23.4331 30.5915 22.2067 31.8603C20.9806 33.129 21.015 35.1588 22.2839 36.3854L25.9099 39.8898C26.5097 40.4697 27.2995 40.789 28.1339 40.789C28.1656 40.789 28.1973 40.7886 28.225 40.7877C29.0809 40.7633 29.9047 40.3885 30.4851 39.7596L37.8432 31.7902C39.0403 30.4937 38.9594 28.4651 37.6629 27.2679C37.0349 26.6882 36.2176 26.3881 35.3644 26.4217V26.4217ZM36.3402 30.4026C36.3402 30.4026 28.9822 38.372 28.982 38.3722C28.5435 38.8471 27.7958 38.8678 27.3315 38.419C27.3293 38.4169 23.7055 34.9145 23.7055 34.9145C23.2476 34.4719 23.2352 33.7396 23.6775 33.282C23.9037 33.048 24.2056 32.9302 24.508 32.9302C24.797 32.9302 25.0864 33.0379 25.3102 33.254C25.3102 33.254 27.3318 35.2077 27.3345 35.2105C27.7362 35.5987 28.417 35.5802 28.7968 35.1689C28.7968 35.1689 34.6253 28.8563 34.6438 28.8364C35.6316 27.7665 37.358 29.3005 36.3402 30.4026V30.4026Z" fill="black"/>
                                    </svg>
                                </div>
                                <div class="info">
                                    <h5 class="mb-0">First Working Process</h5>
                                    <p class="mb-4">
                                        Indulgence contrasted sufficient to unpleasant in in insensible favourable. Latter remark hunted enough vulgar say man. Sitting hearted on it without me. 
                                    </p>
                                </div>
                            </li>
                            <li class="d-flex">
                                <div class="icon others-icon">
                                    <svg width="61" height="64" viewBox="0 0 61 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M34.4638 64H26.3738C23.5706 64 21.2901 61.7194 21.2901 58.9162V55.69C21.2901 54.4909 20.6242 53.3984 19.5525 52.8385C18.9568 52.5276 18.3725 52.1896 17.8155 51.8338C16.792 51.1824 15.5095 51.15 14.4696 51.7496L11.674 53.3643C9.2456 54.7658 6.12997 53.9311 4.72822 51.5037L0.683223 44.4963C0.00422281 43.3208 -0.176027 41.9507 0.175473 40.6389C0.526973 39.3272 1.3681 38.231 2.54372 37.552L5.34135 35.937C6.37947 35.3373 6.99297 34.212 6.94222 33.0006C6.91272 32.3108 6.91285 31.689 6.94222 30.9986C6.99297 29.7881 6.37947 28.6629 5.3411 28.063L2.54385 26.4481C1.3681 25.769 0.526973 24.6728 0.175473 23.3611C-0.176027 22.0493 0.00434774 20.6793 0.683348 19.5036L1.94572 17.3175C2.20472 16.8691 2.77785 16.7153 3.22647 16.9745C3.67485 17.2334 3.82835 17.8068 3.56947 18.2551L2.30697 20.4414C1.87835 21.1834 1.7646 22.0479 1.9866 22.8759C2.2086 23.7039 2.73935 24.3959 3.48147 24.8245L6.27885 26.4394C7.92372 27.3896 8.89572 29.167 8.81547 31.0781C8.78835 31.7168 8.78835 32.2829 8.81547 32.9211C8.89572 34.833 7.92372 36.6105 6.2791 37.5606L3.48135 39.1756C2.73935 39.6041 2.20847 40.2961 1.9866 41.1241C1.76472 41.9521 1.87847 42.8167 2.30697 43.5586L6.35185 50.5662C7.2366 52.0982 9.20335 52.6251 10.7363 51.7405L13.5323 50.1256C15.1802 49.1754 17.2073 49.2244 18.8233 50.2529C19.3358 50.5803 19.8726 50.8908 20.42 51.1764C22.1131 52.0606 23.1648 53.7901 23.1648 55.69V58.9162C23.1648 60.6855 24.6043 62.125 26.3736 62.125H34.4637C36.233 62.125 37.6725 60.6855 37.6725 58.9162V55.69C37.6725 53.7901 38.7242 52.0606 40.4172 51.1765C40.9648 50.8906 41.5016 50.5801 42.0128 50.2537C43.6301 49.2244 45.6573 49.1755 47.3045 50.1254L50.1013 51.7406C51.6338 52.6251 53.6006 52.0984 54.4857 50.5661L58.5305 43.5589C58.9591 42.8168 59.0728 41.9522 58.8508 41.1242C58.6288 40.2962 58.0981 39.6042 57.356 39.1756L54.5586 37.5607C52.9137 36.6105 51.9417 34.8331 52.022 32.922C52.0491 32.2834 52.0491 31.7173 52.022 31.079C51.9417 29.1671 52.9137 27.3896 54.5583 26.4395L57.3561 24.8244C58.0981 24.3959 58.629 23.7039 58.8508 22.8759C59.0727 22.0479 58.959 21.1833 58.5305 20.4414L54.4856 13.4338C53.6007 11.9018 51.634 11.3747 50.1011 12.2595L47.3051 13.8744C45.6571 14.8246 43.6301 14.7756 42.0141 13.7471C41.5016 13.4197 40.9648 13.1092 40.4175 12.8236C38.7243 11.9394 37.6726 10.2099 37.6726 8.31V5.08375C37.6726 3.3145 36.2331 1.875 34.4638 1.875H26.3738C24.6046 1.875 23.1651 3.3145 23.1651 5.08375V8.31C23.1651 10.2099 22.1133 11.9394 20.4203 12.8235C19.8727 13.1094 19.336 13.4199 18.8247 13.7463C17.2075 14.7756 15.1802 14.8245 13.5331 13.8746L10.7362 12.2594C9.2036 11.3749 7.23697 11.9016 6.35185 13.4339L5.43335 15.0259C5.1746 15.4744 4.60122 15.6283 4.15285 15.3695C3.70435 15.1108 3.5506 14.5374 3.80935 14.089L4.7281 12.4965C6.13022 10.0687 9.24585 9.234 11.6737 10.6355L14.4702 12.2506C15.5097 12.85 16.7922 12.8175 17.8166 12.1652C18.3722 11.8102 18.9567 11.4723 19.5525 11.1613C20.6241 10.6015 21.29 9.509 21.29 8.30987V5.08375C21.2901 2.28062 23.5706 0 26.3738 0H34.464C37.2672 0 39.5477 2.28062 39.5477 5.08375V8.31C39.5477 9.50913 40.2136 10.6016 41.2853 11.1615C41.881 11.4724 42.4653 11.8104 43.0223 12.1663C44.0458 12.8175 45.3283 12.85 46.3682 12.2504L49.1638 10.6357C51.5922 9.23412 54.7078 10.069 56.1096 12.4963L60.1545 19.5039C60.8335 20.6794 61.0137 22.0494 60.6622 23.3612C60.3107 24.6729 59.4696 25.7691 58.294 26.4481L55.4963 28.0631C54.4582 28.6629 53.8447 29.7881 53.8955 30.9995C53.925 31.6894 53.9248 32.3111 53.8955 33.0015C53.8447 34.212 54.4582 35.3373 55.4966 35.9371L58.2938 37.5519C59.4696 38.231 60.3107 39.3272 60.6622 40.6389C61.0137 41.9507 60.8333 43.3208 60.1543 44.4964L56.1096 51.5036C55.169 53.1325 53.456 54.0443 51.6976 54.0444C50.8355 54.0444 49.9627 53.8254 49.164 53.3644L46.3675 51.7492C45.328 51.1499 44.0455 51.1824 43.0211 51.8346C42.4655 52.1896 41.881 52.5276 41.2852 52.8386C40.2136 53.3984 39.5477 54.4909 39.5477 55.69V58.9162C39.5476 61.7194 37.2671 64 34.4638 64V64Z" fill="black"/>
                                        <path d="M30.4188 50.8761C20.0105 50.8761 11.5427 42.4082 11.5427 32C11.5427 21.5917 20.0105 13.1239 30.4188 13.1239C40.8272 13.1239 49.295 21.5916 49.295 32C49.295 42.4084 40.8272 50.8761 30.4188 50.8761ZM30.4188 14.9989C21.0445 14.9989 13.4177 22.6256 13.4177 32C13.4177 41.3744 21.0445 49.0011 30.4188 49.0011C39.7932 49.0011 47.42 41.3745 47.42 32C47.42 22.6255 39.7932 14.9989 30.4188 14.9989V14.9989Z" fill="black"/>
                                        <path d="M26.1444 45.7504C25.076 45.7504 24.0827 45.1894 23.5306 44.2661L17.3305 33.896C16.4692 32.4546 16.9407 30.581 18.3812 29.7191L21.5092 27.8501C22.2077 27.4327 23.0272 27.3122 23.8152 27.5104C24.6042 27.7087 25.2686 28.2029 25.6861 28.9015L27.9505 32.6891L33.6487 21.1922C34.3959 19.6874 36.227 19.071 37.7306 19.8175L40.9949 21.4347C42.4997 22.1809 43.1171 24.0117 42.3712 25.5162L36.3324 37.6999C36.1025 38.1637 35.5404 38.3535 35.076 38.1235C34.6121 37.8936 34.4224 37.3311 34.6524 36.8672L40.6912 24.6835C40.978 24.1052 40.7406 23.4015 40.1624 23.1147L36.8976 21.4974C36.3191 21.2105 35.6154 21.4474 35.3284 22.0255L28.8676 35.0614C28.7152 35.3687 28.4072 35.5684 28.0645 35.5819C27.7237 35.5962 27.399 35.4206 27.223 35.1261L24.0767 29.8636C23.7457 29.3095 23.0255 29.1287 22.471 29.4599L19.3434 31.3286C18.79 31.6596 18.6087 32.3801 18.9399 32.9341L25.1397 43.3039C25.3601 43.6724 25.7634 43.89 26.1897 43.8745L30.5845 43.702C31.0116 43.6846 31.396 43.4356 31.5864 43.052L32.9862 40.2274C33.2161 39.7636 33.7786 39.5737 34.2425 39.8036C34.7065 40.0335 34.8961 40.596 34.6662 41.0599L33.2662 43.885C32.7711 44.8827 31.7724 45.5304 30.6594 45.5754L26.2631 45.748C26.2235 45.7496 26.1837 45.7504 26.1444 45.7504V45.7504Z" fill="black"/>
                                    </svg>
                                </div>
                                <div class="info">
                                    <h5 class="mb-0">Audit &amp; Assurance</h5>
                                    <p class="mb-4">
                                        Indulgence contrasted sufficient to unpleasant in in insensible favourable. Latter remark hunted enough vulgar say man. Sitting hearted on it without me. 
                                    </p>
                                </div>
                            </li>
                            <li class="d-flex">
                                <div class="icon others-icon">
                                    <svg width="65" height="65" viewBox="0 0 65 65" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M31.6892 37.8405C31.6892 38.3171 31.3201 38.6862 30.8435 38.6862H25.4008C25.156 38.6895 24.8999 38.6017 24.7396 38.4094C24.4168 38.0558 24.4475 37.533 24.7857 37.2102L29.3367 33.0897C30.1073 32.3988 30.1887 31.1837 29.5562 30.3737C28.9048 29.5396 27.6621 29.4009 26.8141 30.0156C26.4748 30.2617 26.1925 30.7047 25.758 30.7802C25.3524 30.8505 24.9511 30.6001 24.8169 30.2152C24.6031 29.6014 25.1282 29.2341 25.5237 28.8769C26.2156 28.2312 27.1226 27.9236 27.9991 27.9236C28.9985 27.9236 30.0132 28.3387 30.7359 29.1383C31.3817 29.8302 31.6892 30.7372 31.6892 31.6137C31.6892 32.6131 31.274 33.6278 30.4746 34.3505L27.5686 36.9797H30.8435C31.3202 36.9795 31.6892 37.3639 31.6892 37.8405Z" fill="black"/>
                                    <path d="M40.3451 35.8265C40.3451 36.3031 39.9607 36.6721 39.4841 36.6721H39.3303V37.8406C39.3303 38.3172 38.9613 38.6862 38.4847 38.6862C38.0081 38.6862 37.6391 38.3172 37.6391 37.8406V36.6721H32.8267C32.1125 36.6966 31.6939 35.8607 32.1349 35.3037C32.3308 35.0602 37.7593 28.2942 37.8233 28.2316C37.8765 28.1794 37.9449 28.1106 38.0082 28.0696C38.5632 27.7107 39.3306 28.0859 39.3306 28.7693V34.9808H39.4843C39.9607 34.9808 40.3451 35.3651 40.3451 35.8265ZM37.6389 31.1832L34.5792 34.9808H37.6389V31.1832Z" fill="black"/>
                                    <path d="M59.9258 22.3109C57.6073 9.27469 45.5574 0 32.4999 0C19.4422 0 7.39286 9.27494 5.07406 22.3107C2.20302 22.7589 0 25.231 0 28.2049V31.3574C0 32.5854 1.9043 32.5854 1.9043 31.3574V28.2048C1.9043 25.9646 3.74651 24.142 6.01098 24.142H7.99805V43.8996H6.01098C3.74651 43.8998 1.9043 42.0772 1.9043 39.8371V36.1816C1.9043 34.9536 0 34.9536 0 36.1816V39.8371C0 43.1274 2.69648 45.8041 6.01098 45.8041H8.09961C8.98257 49.1009 14.0373 48.452 14.0373 45.0326V23.0094C14.0373 19.5958 8.98422 18.9353 8.09961 22.2379H7.02584C9.36444 10.2939 20.5015 1.9043 32.4999 1.9043C44.4982 1.9043 55.6354 10.2937 57.974 22.2379H56.9004C56.0174 18.9412 50.9625 19.59 50.9625 23.0094V45.0326C50.9625 48.4462 56.0159 49.1069 56.9004 45.8041H57.9615C56.2528 54.8523 47.9819 60.7506 38.9942 61.0353C38.5894 59.8513 37.4609 58.9968 36.1347 58.9968H28.8653C27.2293 58.9968 25.8456 60.3571 25.8456 61.9984C25.8456 63.6398 27.2293 65 28.8653 65H36.1347C37.4689 65 38.6031 64.1351 39.0015 62.9399C49.0716 62.6431 58.1976 55.8779 59.9078 45.7344C62.7876 45.294 65 42.8172 65 39.8371V28.2049C65 25.231 62.7969 22.7589 59.9258 22.3109ZM9.90234 23.0094C9.90234 21.5928 12.133 21.5919 12.133 23.0094V45.0326C12.133 46.4494 9.90234 46.4501 9.90234 45.0326V23.0094ZM36.1347 63.0957H28.8653C27.4311 63.0957 27.4301 60.9011 28.8653 60.9011H36.1347C37.569 60.9011 37.57 63.0957 36.1347 63.0957ZM55.0977 45.0326C55.0977 46.4494 52.8668 46.4501 52.8668 45.0326V23.0094C52.8668 21.5927 55.0977 21.5919 55.0977 23.0094V45.0326ZM63.0957 39.8371C63.0957 42.0773 61.2534 43.8998 58.9888 43.8998H57.002V24.1422H58.9888C61.2534 24.1422 63.0957 25.9647 63.0957 28.2049V39.8371Z" fill="black"/>
                                    <path d="M37.4225 48.0907L43.1482 50.6132C43.7663 50.8858 44.4841 50.4152 44.4841 49.7419V43.3705C46.8981 40.5541 48.223 36.9848 48.223 33.2773C48.223 30.0125 47.2161 26.8862 45.3115 24.236C44.5952 23.2386 43.0484 24.3503 43.7652 25.3474C45.4356 27.6718 46.3186 30.4138 46.3186 33.2773C46.3186 36.6371 45.0775 39.8683 42.8237 42.3756C42.6666 42.5504 42.5797 42.7772 42.5797 43.0122V48.2821L37.8507 46.1987C37.6289 46.1008 37.3779 46.0915 37.1494 46.1723C35.6584 46.6998 34.0941 46.9671 32.4999 46.9671C24.8803 46.9671 18.6813 40.8259 18.6813 33.2773C18.6813 25.7286 24.8804 19.5873 32.5 19.5873C35.3414 19.5873 38.0703 20.4346 40.3915 22.0376C41.4019 22.7353 42.4841 21.1685 41.4735 20.4706C38.8328 18.6469 35.7297 17.683 32.5 17.683C23.8304 17.683 16.7771 24.6785 16.7771 33.2771C16.7771 41.8758 23.8304 48.8713 32.5 48.8713C34.1826 48.8713 35.8366 48.6089 37.4225 48.0907Z" fill="black"/>
                                    </svg>
                                </div>
                                <div class="info">
                                    <h5 class="mb-0">24/7 Live Support</h5>
                                    <p class="mb-4">
                                        Indulgence contrasted sufficient to unpleasant in in insensible favourable. Latter remark hunted enough vulgar say man. Sitting hearted on it without me. 
                                    </p>
                                </div>
                            </li>
                        </ul>
                    </div>

                </div>
            </div>
        </div>
    </section>

    <!-- about details end -->

    <!-- download apps -->

	<section id="vedio-demo">
        <div class="vedio-overlay"></div>
        <div class="container">
        	<div class="row justify-content-center">
	            <div class="col-9 col-md-10 col-lg-9">
	                <div class="section-title vedio-title text-center mb-5">
	                    <h2>Download Our Business Apps</h2>
	                    <p class="lead">Building your Apps helps attract more potential clients. Our integrated marketing team will promote enabled internal or work high-impact convergence.</p>
	                </div>
	                <div class="download-btn-wrap text-center">
	                    <a class="btn btn-pill border border-variant-light  text-white  shadow-hover mr-md-3 mb-4 mb-md-3 mb-lg-0" href="#">
	                        <div class="d-flex align-items-center ">
	                            <span class="icon icon-md mr-3 h-auto">
	                                <svg width="31" height="37" viewBox="0 0 31 37" fill="none" xmlns="http://www.w3.org/2000/svg">
	                                <path d="M10.0505 37C4.54994 36.9683 0 25.7399 0 20.0215C0 10.6805 7.00732 8.63553 9.70789 8.63553C10.9249 8.63553 12.2245 9.11347 13.3707 9.5364C14.1722 9.83141 15.0012 10.136 15.4622 10.136C15.7382 10.136 16.3885 9.87689 16.9627 9.64958C18.1871 9.16212 19.7108 8.55627 21.4851 8.55627C21.4883 8.55627 21.4925 8.55627 21.4956 8.55627C22.8205 8.55627 26.8376 8.84703 29.2527 12.4739L29.8184 13.324L29.0042 13.9384C27.8411 14.816 25.7189 16.4169 25.7189 19.588C25.7189 23.3439 28.1223 24.7883 29.277 25.483C29.7867 25.7896 30.3143 26.1058 30.3143 26.7973C30.3143 27.2488 26.7107 36.944 21.4777 36.944C20.1972 36.944 19.292 36.5591 18.4937 36.2197C17.6859 35.876 16.989 35.58 15.8375 35.58C15.2538 35.58 14.5157 35.856 13.7343 36.1489C12.6665 36.5474 11.4579 37 10.0864 37H10.0505Z" fill="black"/>
	                                <path d="M22.0395 0C22.1758 4.91404 18.6615 8.32317 15.1513 8.10936C14.5729 4.18779 18.6611 0 22.0395 0Z" fill="black"/>
	                                </svg>
	                            </span>
	                            <div class="d-block text-left">
	                                <small class="font-small ">Download on the</small>
	                                <div class="h6 mb-0">App Store</div>
	                            </div>
	                        </div>
	                    </a>
	                    <a class="btn btn-pill border border-variant-light  text-white  shadow-hover mr-md-3 mb-4 mb-md-3 mb-lg-0" href="#">
	                        <div class="d-flex align-items-center">
	                            <span class="icon icon-md mr-3 h-auto">
	                                <svg width="28" height="32" viewBox="0 0 28 32" fill="none" xmlns="http://www.w3.org/2000/svg">
	                                    <path d="M26.5503 13.4451L4.34836 0.399902C3.44084 -0.133301 2.35722 -0.133301 1.44945 0.399902C1.44202 0.404297 1.4353 0.40918 1.42786 0.413574C1.41491 0.421387 1.40196 0.429443 1.38924 0.437988C0.518656 0.977783 0 1.9126 0 2.95483V29.0452C0 30.1118 0.541926 31.0669 1.44945 31.6001C1.90334 31.8667 2.40112 32 2.89891 32C3.39669 32 3.89472 31.8667 4.34836 31.6001L26.5505 18.5549C27.4581 18.0217 28 17.0667 28 16C28 14.9333 27.4581 13.9783 26.5503 13.4451V13.4451ZM1.86568 29.2671C1.85224 29.1985 1.84408 29.125 1.84408 29.0452V2.95483C1.84408 2.81445 1.86735 2.69214 1.90454 2.58569L14.4528 15.9058L1.86568 29.2671ZM4.55707 2.68945L18.9413 11.1411L15.7303 14.5498L4.55707 2.68945ZM4.15909 29.5444L15.73 17.2615L19.0557 20.7917L4.15909 29.5444ZM25.6284 16.9297L20.6999 19.8254L17.0075 15.9058L20.5855 12.1074L25.6284 15.0703C26.1043 15.3501 26.1559 15.8137 26.1559 16C26.1559 16.1863 26.1043 16.6499 25.6284 16.9297V16.9297Z" fill="black"/>
	                                </svg>
	                            </span>
	                            <div class="d-block text-left">
	                                <small class="font-small ">Download on the</small>
	                                <div class="h6 mb-0">Google Play</div>
	                            </div>
	                        </div>
	                    </a>
	                    <a class="btn btn-pill border border-variant-light  text-white  shadow-hover mb-lg-0 mb-4" href="#">
	                        <div class="d-flex align-items-center">
	                            <span class="icon icon-meta icon-md mr-3 h-auto">
	                                <svg width="33" height="31" viewBox="0 0 33 31" fill="none" xmlns="http://www.w3.org/2000/svg">
	                                    <path d="M18.8116 1.53349L15.9722 12.8854C12.517 11.4404 7.90102 10.8088 3.80991 12.0749C4.63765 8.43805 6.65076 1.53349 6.65076 1.53349C9.65888 -0.929538 15.5107 -0.045558 18.8116 1.53349Z" fill="black"/>
	                                    <path d="M20.4541 3.18897C23.5502 5.00164 29.0243 5.56115 33 4.37434L29.7539 15.3181C26.4185 17.0832 20.0417 16.952 17.5931 14.5062L20.4541 3.18897Z" fill="black"/>
	                                    <path d="M14.9945 15.2561L12.158 26.6051C8.58456 24.9611 4.0291 23.8623 0 25.7932C0 25.7932 1.87035 18.4834 2.83508 14.8509C5.95713 12.9142 12.1767 13.4867 14.9945 15.2561Z" fill="black"/>
	                                    <path d="M16.9384 16.8827C20.0345 19.0718 25.2965 19.4265 29.1858 17.921L25.9455 28.8691C22.8075 31.0019 16.6687 30.6746 13.7817 28.4639L16.9384 16.8827Z" fill="black"/>
	                                </svg>
	                            </span>
	                            <div class="d-block text-left">
	                                <small class="font-small ">Download on the</small>
	                                <div class="h6 mb-0">Windows</div>
	                            </div>
	                        </div>
	                    </a>
	                </div>
	            </div>
	        </div>
        </div>
    </section>

    <!-- download apps end -->

    <!-- how it work -->
    <section id="corpo-working-process">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="section-heading text-center mb-5">
                        <h2>How Does it Work?</h2>
                        <p class="lead">Distinctively grow go forward manufactured products and enthusiastically disseminate outsourcing customer service.</p>
                    </div>
                </div>
            </div>
            <div class="row align-items-center justify-content-center">
                <div class="col-lg-4 col-md-6 mb-4 mb-md-4 mb-lg-0">
                    <div class="feature-widget text-center p-4 ">
                        <div class="p-3 p-md-4 d-inline-flex rounded text-white icon icon-shape icon-lg mb-4">
                            <svg width="50" height="50" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M0.734451 18.6948C0.898977 18.8088 1.07256 18.9091 1.25345 18.9948C1.46995 19.1053 1.83195 19.2903 1.85745 19.4203C1.88927 19.591 1.86889 19.7674 1.79895 19.9263C1.72445 20.0863 1.64595 20.2238 1.57895 20.3428C1.39545 20.6658 1.08945 21.2073 1.55145 21.5853C1.65945 21.6853 1.84595 21.8298 2.01645 21.9603C1.89116 22.1791 1.83438 22.4304 1.85345 22.6818C1.87971 22.8786 1.98588 23.0558 2.14695 23.1718C2.25945 23.2518 2.37995 23.3218 2.49695 23.3893C2.79095 23.5568 2.90595 23.6348 2.94695 23.7893C2.97384 23.9927 2.95505 24.1996 2.89195 24.3948C2.86245 24.5248 2.83395 24.6543 2.81545 24.7873C2.76589 25.2434 2.82377 25.7047 2.98445 26.1343C3.20885 26.9305 3.92886 27.4853 4.75595 27.4993C4.89322 27.4988 5.03008 27.4844 5.16445 27.4563C6.59286 27.1178 8.00039 26.6966 9.37995 26.1948C10.5059 26.8558 11.218 28.8658 11.4035 29.6203C11.459 29.8432 11.6593 29.9995 11.889 29.9993L24.439 29.9643C24.598 29.964 24.7474 29.8881 24.8414 29.7597C24.9353 29.6314 24.9627 29.466 24.9149 29.3143C24.7913 28.9756 24.6208 28.6558 24.4084 28.3643C24.2883 28.1919 24.1801 28.0114 24.0845 27.8243C23.8584 27.3657 23.6696 26.8897 23.5199 26.4008C23.2347 25.4345 23.0368 24.4445 22.9285 23.4428C22.7529 21.9469 22.9797 20.4313 23.5854 19.0523C23.9551 18.2875 24.4107 17.5674 24.9434 16.9058C25.2434 16.5008 25.555 16.0813 25.8345 15.6408C26.315 14.8943 26.6634 14.0706 26.8644 13.2058C26.922 12.9389 26.7547 12.6751 26.4887 12.6134C26.2227 12.5517 25.9564 12.7148 25.8904 12.9798C25.7147 13.7346 25.4102 14.4534 24.9905 15.1048C24.731 15.5153 24.4309 15.9193 24.1404 16.3103C23.5619 17.0322 23.0682 17.8182 22.6689 18.6528C21.9918 20.195 21.7388 21.8901 21.9359 23.5628C22.0513 24.6252 22.2623 25.6749 22.5664 26.6993C22.7307 27.2372 22.9382 27.7609 23.1869 28.2653C23.3009 28.4907 23.4304 28.7079 23.5745 28.9153L23.6079 28.9653L12.2669 28.9993C11.9346 27.8133 11.332 26.7204 10.5065 25.8063L10.9565 25.6498C11.2925 25.5323 11.546 25.4438 11.677 25.3998C13.818 24.7173 14.889 23.7423 14.8595 22.4998C14.8581 22.3671 14.803 22.2406 14.7068 22.1491C14.6107 22.0577 14.4816 22.009 14.349 22.0143C14.073 22.0209 13.8545 22.2498 13.861 22.5258C13.873 23.0373 13.4514 23.7863 11.3729 24.4483C11.2369 24.4918 10.973 24.5833 10.6275 24.7048C10.256 24.8343 9.78495 24.9993 9.26495 25.1758H9.26145C7.85531 25.6914 6.42059 26.1255 4.96445 26.4758C4.49873 26.5634 4.04602 26.2709 3.93445 25.8103C3.82692 25.5281 3.78469 25.2252 3.81095 24.9243C3.82545 24.8243 3.84895 24.7193 3.87195 24.6173C3.97836 24.2683 3.99555 23.8982 3.92195 23.5408C3.77674 23.0829 3.43999 22.7106 2.99895 22.5203L2.89145 22.4588C2.94535 22.3493 3.01 22.2455 3.08445 22.1488C3.16822 22.0424 3.20525 21.9066 3.18706 21.7724C3.16887 21.6382 3.09703 21.5171 2.98795 21.4368C2.86845 21.3488 2.58795 21.1368 2.37795 20.9698C2.40195 20.9263 2.42795 20.8783 2.45345 20.8353C2.53195 20.6973 2.62195 20.5353 2.70895 20.3518C2.86741 20.0016 2.91386 19.6109 2.84195 19.2333C2.72245 18.6233 2.13795 18.3243 1.71145 18.1063C1.56776 18.038 1.42996 17.9579 1.29945 17.8668C1.1923 17.7951 1.09189 17.7137 0.999451 17.6238C0.999451 17.5478 1.08095 17.3643 1.46145 16.9008C2.35616 15.748 3.12315 14.5016 3.74895 13.1833C3.86643 12.9767 3.93261 12.7448 3.94195 12.5073C3.88959 12.0042 3.62211 11.5484 3.20845 11.2573C3.00595 11.0713 2.81445 10.8963 2.79295 10.7488C2.79753 10.6263 2.82501 10.5057 2.87395 10.3933C2.95545 10.1793 3.04995 9.96932 3.14395 9.75882C3.35479 9.32453 3.52227 8.8705 3.64395 8.40332C3.75853 7.84295 3.90883 7.29049 4.09395 6.74932C4.48805 5.66142 5.09831 4.66457 5.88795 3.81882C6.84395 2.80632 8.34895 2.09032 10.369 1.68932C11.9141 1.35009 13.4819 1.12415 15.0599 1.01332C15.5909 0.985955 16.1231 0.997319 16.6524 1.04732C16.8307 1.06393 17.0043 0.984172 17.1078 0.838087C17.2114 0.692002 17.2291 0.501785 17.1543 0.339087C17.0796 0.17639 16.9237 0.0659298 16.7454 0.049317C16.1677 -0.00362231 15.5869 -0.0151518 15.0075 0.014817C13.3807 0.127694 11.7643 0.359649 10.1715 0.708817C7.91695 1.15882 6.27845 1.94932 5.16045 3.13132C4.27761 4.07508 3.59521 5.18798 3.15445 6.40282C2.95182 6.994 2.78732 7.59757 2.66195 8.20982C2.55509 8.60245 2.41145 8.98414 2.23295 9.34982C2.13295 9.57732 2.02995 9.80482 1.93995 10.0413C1.82616 10.3057 1.77944 10.5941 1.80395 10.8808C1.90738 11.3271 2.16679 11.7218 2.53545 11.9938C2.78045 12.2183 2.94195 12.3763 2.94395 12.5143C2.92575 12.5902 2.89715 12.6632 2.85895 12.7313C2.26032 13.9921 1.5267 15.1843 0.670951 16.2868C0.308951 16.7283 -0.0710492 17.2428 0.00845076 17.7578C0.114923 18.1549 0.376529 18.4925 0.734451 18.6948Z" fill="black"/>
                                <path d="M5.969 7.44451C6.2701 6.60975 6.73677 5.84443 7.341 5.19451C8.0075 4.48901 9.16 3.96951 10.767 3.65001C11.0379 3.5963 11.214 3.33315 11.1602 3.06226C11.1065 2.79136 10.8434 2.6153 10.5725 2.66901C8.758 3.03001 7.4265 3.64801 6.6145 4.50601C5.91678 5.25486 5.37795 6.13726 5.0305 7.10001C4.86218 7.59951 4.72508 8.10899 4.62 8.62551L5.5965 8.84201C5.69233 8.36908 5.81673 7.90239 5.969 7.44451Z" fill="black"/>
                                <path d="M16.4295 2.8L16.1295 4.296C15.8403 4.39097 15.5588 4.50798 15.2875 4.646L14.018 3.796C13.6213 3.53624 13.0968 3.59074 12.762 3.9265L11.932 4.756C11.5896 5.09213 11.5342 5.62404 11.8 6.0235L12.646 7.287C12.508 7.55831 12.391 7.8398 12.296 8.129L10.802 8.429C10.3352 8.5233 9.99962 8.93374 10 9.41V10.59C9.99967 11.0655 10.3342 11.4754 10.8 11.5705L12.296 11.8705C12.391 12.1597 12.508 12.4412 12.646 12.7125L11.796 13.982C11.5362 14.3787 11.5907 14.9032 11.9265 15.238L12.7565 16.068C13.0932 16.4088 13.624 16.4639 14.0235 16.1995L15.287 15.3535C15.5583 15.4915 15.8398 15.6085 16.129 15.7035L16.429 17.1975C16.5231 17.6645 16.9336 18.0003 17.41 18H18.59C19.0655 18.0003 19.4754 17.6658 19.5705 17.2L19.8705 15.704C20.1597 15.609 20.4412 15.492 20.7125 15.354L21.982 16.204C22.3787 16.4638 22.9032 16.4093 23.238 16.0735L24.0675 15.244C24.4099 14.9079 24.4653 14.376 24.1995 13.9765L23.3535 12.713C23.4915 12.4417 23.6085 12.1602 23.7035 11.871L25.1975 11.571C25.4943 11.511 25.7476 11.3193 25.886 11.05L26.6395 11.555C26.7849 11.6466 26.9532 11.6953 27.125 11.6955C27.3647 11.6956 27.5945 11.6 27.7635 11.43L28.4285 10.765C28.7309 10.461 28.7795 9.98697 28.545 9.628L27.9 8.663C27.9936 8.47496 28.0744 8.28086 28.142 8.082L29.2735 7.8565C29.6969 7.77333 30.0016 7.40144 30 6.97V6.03C30.0015 5.60018 29.6989 5.22927 29.2775 5.1445L28.141 4.918C28.0739 4.7191 27.9934 4.52498 27.9 4.337L28.555 3.3605C28.782 3.00286 28.73 2.53555 28.43 2.2365L27.765 1.5715C27.4611 1.2689 26.9869 1.22032 26.628 1.455L25.663 2.1C25.475 2.00645 25.2809 1.9256 25.082 1.858L24.856 0.726505C24.7729 0.303344 24.4013 -0.00137475 23.97 5.67604e-06H23.03C22.6002 -0.00151111 22.2293 0.30112 22.1445 0.722505L21.918 1.859C21.7191 1.92656 21.5249 2.00758 21.337 2.1015L20.3605 1.4465C20.0028 1.21993 19.5357 1.27187 19.2365 1.5715L18.7865 2.0215C18.7218 2.00802 18.656 2.00082 18.59 2H17.41C16.9345 1.99967 16.5246 2.33415 16.4295 2.8ZM24.0735 4.76L23.84 4.5265C24.7067 4.64252 25.396 5.31078 25.5388 6.17342C25.6816 7.03606 25.2445 7.89084 24.4615 8.28L23.7025 8.13C23.6075 7.8408 23.4905 7.55931 23.3525 7.288L24.2025 6.0185C24.4632 5.62197 24.4093 5.09682 24.0735 4.7615V4.76ZM19.8885 2.332L21.0315 3.1C21.1918 3.20749 21.3996 3.21313 21.5655 3.1145C21.8517 2.94605 22.1596 2.81737 22.4805 2.732C22.6639 2.68248 22.8034 2.5333 22.8405 2.347L23.109 1.00001H23.891L24.1595 2.35C24.1966 2.5363 24.3361 2.68548 24.5195 2.735C24.8405 2.82037 25.1483 2.94905 25.4345 3.1175C25.601 3.21524 25.8088 3.20842 25.9685 3.1L27.1115 2.333L27.6665 2.888L26.9 4.0315C26.7925 4.19179 26.7869 4.39962 26.8855 4.5655C27.054 4.85172 27.1826 5.15955 27.268 5.4805C27.3175 5.6639 27.4667 5.80338 27.653 5.8405L29.003 6.109V6.891L27.6555 7.1595C27.4692 7.19662 27.32 7.33611 27.2705 7.5195C27.1851 7.84045 27.0565 8.14828 26.888 8.4345C26.7894 8.60038 26.795 8.80822 26.9025 8.9685L27.6695 10.1115L27.1145 10.6665L26 9.921V9.41C26.0001 9.11725 25.8722 8.83909 25.65 8.6485C26.5303 7.8005 26.8065 6.50245 26.3477 5.36954C25.8889 4.23663 24.7873 3.49653 23.565 3.5C23.2627 3.5019 22.9626 3.55117 22.6755 3.646C22.4312 3.61041 22.1823 3.66517 21.9755 3.8L20.713 4.646C20.4417 4.50798 20.1602 4.39097 19.871 4.296L19.571 2.802C19.5622 2.76194 19.5506 2.72252 19.5365 2.684L19.8885 2.332ZM17.05 4.7885L17.41 3H18.59L18.9495 4.79C18.9869 4.97579 19.1261 5.12447 19.309 5.174C19.7221 5.28537 20.119 5.45009 20.4895 5.664C20.6546 5.75978 20.8599 5.75318 21.0185 5.647L22.5315 4.635L23.3695 5.4685L22.3545 6.983C22.2483 7.14163 22.2417 7.34688 22.3375 7.512C22.5514 7.88255 22.7161 8.27939 22.8275 8.6925C22.877 8.87542 23.0257 9.01462 23.2115 9.052L25 9.4095V10.59L23.21 10.9495C23.0242 10.9869 22.8755 11.1261 22.826 11.309C22.7146 11.7221 22.5499 12.119 22.336 12.4895C22.2402 12.6546 22.2468 12.8599 22.353 13.0185L23.365 14.5315L22.5315 15.3695L21.017 14.3545C20.8584 14.2483 20.6531 14.2417 20.488 14.3375C20.1175 14.5514 19.7206 14.7161 19.3075 14.8275C19.1246 14.877 18.9854 15.0257 18.948 15.2115L18.59 17H17.41L17.0505 15.21C17.0131 15.0242 16.8739 14.8755 16.691 14.826C16.2779 14.7146 15.881 14.5499 15.5105 14.336C15.3454 14.2402 15.1401 14.2468 14.9815 14.353L13.4685 15.365L12.6305 14.5315L13.6455 13.017C13.7517 12.8584 13.7583 12.6531 13.6625 12.488C13.4486 12.1175 13.2839 11.7206 13.1725 11.3075C13.123 11.1246 12.9743 10.9854 12.7885 10.948L11 10.59V9.41L12.79 9.0505C12.9758 9.01312 13.1245 8.87392 13.174 8.691C13.2854 8.27789 13.4501 7.88105 13.664 7.5105C13.7598 7.34538 13.7532 7.14013 13.647 6.9815L12.635 5.4685L13.4685 4.6305L14.983 5.6455C15.1416 5.75169 15.3469 5.75828 15.512 5.6625C15.8825 5.44859 16.2794 5.28387 16.6925 5.1725C16.8746 5.12233 17.013 4.97377 17.05 4.7885Z" fill="black"/>
                                <path d="M18 13.5C19.933 13.5 21.5 11.933 21.5 10C21.5 8.067 19.933 6.5 18 6.5C16.067 6.5 14.5 8.067 14.5 10C14.5022 11.9321 16.0679 13.4978 18 13.5ZM18 7.5C19.3807 7.5 20.5 8.61929 20.5 10C20.5 11.3807 19.3807 12.5 18 12.5C16.6193 12.5 15.5 11.3807 15.5 10C15.5017 8.61997 16.62 7.50165 18 7.5Z" fill="black"/>
                                <path d="M18 10.5C18.2761 10.5 18.5 10.2761 18.5 10C18.5 9.72386 18.2761 9.5 18 9.5C17.7239 9.5 17.5 9.72386 17.5 10C17.5 10.2761 17.7239 10.5 18 10.5Z" fill="black"/>
                                <path d="M4.5 11C4.77614 11 5 10.7761 5 10.5C5 10.2239 4.77614 10 4.5 10C4.22386 10 4 10.2239 4 10.5C4 10.7761 4.22386 11 4.5 11Z" fill="black"/>
                                </g>
                                <defs>
                                <clipPath id="clip1">
                                <rect width="30" height="30" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <div class="widget-text">
                            <h3 class="h5">Generate Ideas</h3>
                            <p class="mb-0">Appropriately restore mission-critical strategic theme areas rather than visionary ideas.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4 mb-md-4 mb-lg-0">
                    <div class="feature-widget text-center p-4">
                        <div class="p-md-4 rounded d-inline-flex text-white icon icon-shape icon-lg mb-4">
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M8.79098 23.1698C8.93487 23.3106 9.12812 23.3888 9.32889 23.3874C9.52966 23.386 9.72183 23.3052 9.86379 23.1624C10.0058 23.0196 10.0861 22.8264 10.0875 22.6245C10.0888 22.4226 10.011 22.2283 9.87101 22.0836L1.84642 14.0141L4.47673 11.3983L6.64887 13.5827L16.5557 23.545L0.87218 39.3164C0.683032 39.5066 0.532991 39.7324 0.430625 39.9809C0.328258 40.2295 0.27557 40.4958 0.27557 40.7648C0.27557 41.0338 0.328258 41.3001 0.430625 41.5486C0.532991 41.7971 0.683032 42.023 0.87218 42.2132L8.02017 49.4012C8.40247 49.7847 8.92041 50 9.4604 50C10.0004 50 10.5183 49.7847 10.9006 49.4012L26.5842 33.6298L37.8909 44.9998C37.9234 45.0318 37.9588 45.0609 37.9965 45.0866C38.0068 45.0939 38.0173 45.1006 38.028 45.1073C38.0607 45.128 38.095 45.1462 38.1306 45.1617C38.136 45.164 38.1413 45.1672 38.1469 45.1695C38.18 45.1828 38.214 45.1938 38.2487 45.2024L46.4087 47.2242C46.5364 47.2558 46.67 47.2537 46.7966 47.2182C46.9232 47.1827 47.0386 47.115 47.1315 47.0215C47.2245 46.928 47.2919 46.812 47.3272 46.6847C47.3625 46.5573 47.3645 46.423 47.3331 46.2946L45.3225 38.0889C45.314 38.054 45.303 38.0198 45.2898 37.9865C45.2875 37.9809 45.2843 37.9756 45.2819 37.97C45.2665 37.9343 45.2485 37.8998 45.2279 37.8669C45.2213 37.8562 45.2144 37.8457 45.2071 37.8352C45.1816 37.7972 45.1527 37.7617 45.1208 37.7289L33.8143 26.359L49.4034 10.6838C49.5925 10.4936 49.7426 10.2678 49.8449 10.0192C49.9473 9.77074 50 9.50438 50 9.2354C50 8.96641 49.9473 8.70006 49.8449 8.45155C49.7426 8.20304 49.5925 7.97724 49.4034 7.78704L42.2554 0.598882C41.8731 0.215375 41.3552 0 40.8152 0C40.2752 0 39.7572 0.215375 39.3749 0.598882L35.2774 4.71929L23.7862 16.2749L11.1786 3.59635C11.0354 3.45231 10.8411 3.3714 10.6386 3.3714C10.436 3.3714 10.2417 3.45231 10.0985 3.59635L9.72474 3.9722L8.69028 2.91454C8.3356 2.54966 7.91177 2.25998 7.44386 2.06262C6.97594 1.86526 6.47343 1.76422 5.96603 1.76548H5.96323C5.20685 1.76553 4.46747 1.99111 3.83859 2.41372C3.20971 2.83632 2.71956 3.43696 2.43013 4.13968C2.1407 4.84241 2.06498 5.61566 2.21256 6.36165C2.36014 7.10765 2.72437 7.79288 3.25921 8.33072L4.31824 9.39542L0.226761 13.4649C0.155181 13.5361 0.0982976 13.6208 0.0593824 13.7141C0.0204671 13.8075 0.000286968 13.9076 3.03843e-06 14.0089C-0.000280891 14.1101 0.0193369 14.2104 0.0577279 14.304C0.0961188 14.3975 0.152526 14.4826 0.223706 14.5542L8.79098 23.1698ZM43.501 38.2727L38.4314 43.3705L8.2693 13.0397L13.3388 7.94168L43.501 38.2727ZM9.82085 48.3156C9.72521 48.4115 9.59568 48.4653 9.46065 48.4653C9.32562 48.4653 9.19609 48.4115 9.10045 48.3156L1.95246 41.1276C1.90518 41.08 1.86767 41.0236 1.84207 40.9615C1.81648 40.8993 1.80331 40.8327 1.80331 40.7655C1.80331 40.6982 1.81648 40.6317 1.84207 40.5695C1.86767 40.5074 1.90518 40.451 1.95246 40.4034L12.6416 29.6544L16.0533 33.0852C16.124 33.1575 16.2083 33.2149 16.3012 33.2543C16.3941 33.2937 16.4938 33.3141 16.5947 33.3145C16.6955 33.3149 16.7954 33.2952 16.8886 33.2566C16.9819 33.218 17.0666 33.1612 17.1379 33.0895C17.2091 33.0178 17.2656 32.9326 17.304 32.8388C17.3424 32.7451 17.3619 32.6446 17.3615 32.5432C17.3611 32.4418 17.3407 32.3415 17.3015 32.2481C17.2624 32.1547 17.2052 32.07 17.1333 31.9989L13.7216 28.5681L17.6363 24.6316L25.5045 32.5438L9.82085 48.3156ZM39.9291 44.037L44.1637 39.7788L45.5481 45.4291L39.9291 44.037ZM28.0921 14.1174L31.5038 17.5482C31.5745 17.6204 31.6587 17.6779 31.7517 17.7173C31.8446 17.7566 31.9443 17.7771 32.0451 17.7775C32.146 17.7779 32.2459 17.7582 32.3391 17.7196C32.4323 17.681 32.517 17.6242 32.5883 17.5525C32.6596 17.4808 32.7161 17.3956 32.7545 17.3018C32.7928 17.2081 32.8124 17.1076 32.812 17.0062C32.8116 16.9048 32.7912 16.8045 32.752 16.7111C32.7128 16.6176 32.6557 16.5329 32.5838 16.4618L29.1721 13.031L35.8172 6.34866L37.8364 8.37911C37.9069 8.45202 37.9912 8.5101 38.0843 8.54998C38.1774 8.58987 38.2774 8.61076 38.3786 8.61144C38.4798 8.61213 38.5801 8.59259 38.6737 8.55397C38.7673 8.51535 38.8523 8.45841 38.9238 8.38646C38.9954 8.31451 39.052 8.22899 39.0904 8.13486C39.1288 8.04072 39.1482 7.93986 39.1475 7.83812C39.1468 7.73637 39.126 7.63579 39.0864 7.54219C39.0467 7.44859 38.9889 7.36386 38.9164 7.2929L36.8973 5.26272L40.4548 1.68522C40.5505 1.58936 40.68 1.53553 40.815 1.53553C40.95 1.53553 41.0795 1.58936 41.1751 1.68522L48.3232 8.87324C48.4185 8.96937 48.4721 9.09958 48.4721 9.23533C48.4721 9.37108 48.4185 9.50129 48.3232 9.59742L32.7346 25.2735L24.8664 17.3611L28.0921 14.1174ZM12.2588 6.85548L7.18914 11.9535L5.56885 10.3242L10.6385 5.2261L12.2588 6.85548ZM4.33975 7.24477C4.01853 6.92177 3.79977 6.51023 3.71113 6.0622C3.6225 5.61417 3.66797 5.14978 3.8418 4.72774C4.01563 4.3057 4.31001 3.94498 4.68771 3.69118C5.06542 3.43739 5.50947 3.30192 5.96374 3.30191H5.96539C6.27008 3.3012 6.57182 3.36191 6.85278 3.48044C7.13375 3.59897 7.38824 3.77293 7.60122 3.99204L8.64509 5.05866L5.54797 8.17249L5.40412 8.31548L4.33975 7.24477Z" fill="black"/>
                            </svg>
                        </div>
                        <div class="widget-text">
                            <h3 class="h5">Create Design</h3>
                            <p class="mb-0">Quickly redefine granular schemas after top-line total linkage.Appropriately restore.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4 mb-md-4 mb-lg-0">
                    <div class="feature-widget text-center p-4">
                        <div class="p-3 p-md-4 rounded d-inline-flex text-white icon icon-shape icon-lg mb-4">
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M19.0765 33.1333V39.192C19.0765 39.3474 19.1383 39.4964 19.2482 39.6063C19.358 39.7162 19.5071 39.7779 19.6625 39.7779H28.4109C28.5663 39.7779 28.7154 39.7162 28.8252 39.6063C28.9351 39.4964 28.9969 39.3474 28.9969 39.192V33.1333C28.9969 32.9779 28.9351 32.8289 28.8252 32.719C28.7154 32.6091 28.5663 32.5474 28.4109 32.5474H19.6625C19.5071 32.5474 19.358 32.6091 19.2482 32.719C19.1383 32.8289 19.0765 32.9779 19.0765 33.1333V33.1333ZM20.2484 33.7192H27.825V38.6061H20.2484V33.7192Z" fill="black"/>
                                <path d="M16.879 30.3996C16.9613 30.5314 17.0925 30.6252 17.2439 30.6603C17.3953 30.6954 17.5544 30.6689 17.6863 30.5866L25.1086 25.9558C25.2404 25.8735 25.3342 25.7422 25.3693 25.5909C25.4043 25.4395 25.3778 25.2804 25.2955 25.1485L22.0885 20.0082C22.0063 19.8764 21.875 19.7826 21.7236 19.7475C21.5722 19.7125 21.4131 19.739 21.2813 19.8212L13.8594 24.4521C13.7941 24.4927 13.7374 24.5459 13.6926 24.6085C13.6479 24.6711 13.6159 24.7419 13.5984 24.8168C13.581 24.8918 13.5786 24.9694 13.5912 25.0454C13.6038 25.1213 13.6312 25.194 13.6719 25.2593L16.879 30.3996ZM21.4044 21.1256L23.9911 25.2717L17.5631 29.2822L14.9764 25.1361L21.4044 21.1256Z" fill="black"/>
                                <path d="M3.61328 15.7227C3.45788 15.7227 3.30885 15.7844 3.19896 15.8943C3.08908 16.0042 3.02734 16.1532 3.02734 16.3086V16.5039C3.02734 16.6593 3.08908 16.8083 3.19896 16.9182C3.30885 17.0281 3.45788 17.0898 3.61328 17.0898C3.76868 17.0898 3.91772 17.0281 4.0276 16.9182C4.13749 16.8083 4.19922 16.6593 4.19922 16.5039V16.3086C4.19922 16.1532 4.13749 16.0042 4.0276 15.8943C3.91772 15.7844 3.76868 15.7227 3.61328 15.7227Z" fill="black"/>
                                <path d="M3.61328 18.457C3.45788 18.457 3.30885 18.5188 3.19896 18.6286C3.08908 18.7385 3.02734 18.8876 3.02734 19.043V28.0273C3.02734 28.1827 3.08908 28.3318 3.19896 28.4417C3.30885 28.5515 3.45788 28.6133 3.61328 28.6133C3.76868 28.6133 3.91772 28.5515 4.0276 28.4417C4.13749 28.3318 4.19922 28.1827 4.19922 28.0273V19.043C4.19922 18.8876 4.13749 18.7385 4.0276 18.6286C3.91772 18.5188 3.76868 18.457 3.61328 18.457Z" fill="black"/>
                                <path d="M44.7294 11.3778H34.4453V7.77685C34.4438 6.68469 34.0454 5.63031 33.3242 4.81008C32.6031 3.98985 31.6084 3.4597 30.5254 3.31835V3.31318C30.5254 3.15778 30.4637 3.00874 30.3538 2.89886C30.2439 2.78897 30.0949 2.72724 29.9395 2.72724H20.0605C19.9051 2.72724 19.7561 2.78897 19.6462 2.89886C19.5363 3.00874 19.4746 3.15778 19.4746 3.31318V3.31835C18.3916 3.4597 17.3969 3.98985 16.6758 4.81008C15.9546 5.63031 15.5562 6.68469 15.5547 7.77685V11.3778H5.27061C4.00269 11.3792 2.78711 11.8836 1.89056 12.7801C0.99401 13.6767 0.489703 14.8922 0.488281 16.1602V42.4902C0.489729 43.7581 0.994044 44.9737 1.89059 45.8702C2.78714 46.7668 4.0027 47.2711 5.27061 47.2726H44.7294C45.9973 47.2711 47.2129 46.7668 48.1094 45.8702C49.006 44.9737 49.5103 43.7581 49.5117 42.4902V16.1602C49.5103 14.8922 49.006 13.6767 48.1094 12.7801C47.2129 11.8836 45.9973 11.3792 44.7294 11.3778ZM30.5254 4.50302C31.2948 4.63959 31.9916 5.04237 32.494 5.64086C32.9964 6.23936 33.2723 6.99546 33.2734 7.77685V11.3778H31.6699V7.77685C31.6693 7.42183 31.559 7.07567 31.3541 6.78572C31.1493 6.49578 30.8598 6.2762 30.5254 6.15703V4.50302ZM20.6465 3.89912H29.3535V6.50468H20.6465V3.89912ZM19.502 7.77685C19.502 7.66255 19.5378 7.55113 19.6045 7.4583C19.6593 7.52647 19.7288 7.58147 19.8077 7.61924C19.8866 7.65702 19.973 7.6766 20.0604 7.67656H29.9395C30.0269 7.6766 30.1133 7.65702 30.1922 7.61924C30.2711 7.58147 30.3406 7.52647 30.3954 7.4583C30.4621 7.55113 30.498 7.66255 30.498 7.77685V11.3778H19.502V7.77685ZM16.7265 7.77685C16.7276 6.99546 17.0036 6.23936 17.5059 5.64086C18.0083 5.04237 18.7051 4.63959 19.4745 4.50302V6.15703C19.1401 6.27622 18.8507 6.4958 18.6458 6.78574C18.441 7.07569 18.3307 7.42184 18.3301 7.77685V11.3778H16.7266L16.7265 7.77685ZM6.05469 46.1009H5.27051C4.31327 46.0998 3.39557 45.719 2.71873 45.0421C2.04189 44.3652 1.66119 43.4475 1.66016 42.4902V16.1602C1.66124 15.2029 2.04198 14.2852 2.71883 13.6084C3.39569 12.9315 4.31339 12.5508 5.27061 12.5497H6.05469V46.1009ZM38.5769 46.1009H11.423V22.7979C11.423 22.6425 11.3613 22.4935 11.2514 22.3836C11.1415 22.2737 10.9925 22.212 10.8371 22.212C10.6817 22.212 10.5327 22.2737 10.4228 22.3836C10.3129 22.4935 10.2512 22.6425 10.2512 22.7979V46.1009H7.22656V12.5497H10.2511V17.9151C10.2511 18.0705 10.3128 18.2196 10.4227 18.3295C10.5326 18.4393 10.6816 18.5011 10.837 18.5011C10.9924 18.5011 11.1414 18.4393 11.2513 18.3295C11.3612 18.2196 11.4229 18.0705 11.4229 17.9151V12.5497H38.577L38.5769 46.1009ZM42.7732 46.1009H39.7488V12.5497H42.7734L42.7732 46.1009ZM48.3398 42.4902C48.3388 43.4475 47.958 44.3652 47.2812 45.042C46.6043 45.7189 45.6866 46.0996 44.7294 46.1007H43.9453V12.5497H44.7295C45.6867 12.5508 46.6044 12.9316 47.2812 13.6084C47.958 14.2853 48.3388 15.203 48.3398 16.1602V42.4902Z" fill="black"/>
                            </svg> 
                        </div>
                        <div class="widget-text">
                            <h3 class="h5">Launch Project</h3>
                            <p class="mb-0">Appropriately restore mission-critical strategic theme areas rather than visionary ideas.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- how it work end -->

    <!-- start testimonials -->
    <!--testimonial area-->
    <section class="testimonial-area">
        <div class="testimonials-overlay"></div>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-9 col-lg-8">
                    <div class="section-heading mb-0 text-center">
                        <h2>What Clients Say About Us</h2>
                        <p class="mb-0">
                            Rapidiously morph transparent internal or "organic" sources whereas resource sucking
                            e-business innovate compelling internal.
                        </p>
                    </div>
                </div>
            </div>
            <div class="testimonials wow fadeInUp">
                <div class="testimonial">
                    <div class="author-img">
                        <img src="image/testimonials-client.png" alt="" class="img-fluid rounded">
                    </div>
                    <div class="author-quote">
                        <h4>Jhon Henricks</h4>
                        <span>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip2">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip31">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip32">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip4">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip5">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>                                                                                         
                            </span>
                            <span>(5 Out of 5)</span>
                        <blockquote> <i class="fa fa-quote-left"></i> Maecenas varius finibus orci vel dignissim. Nam posuere, magna pellentesque accumsan tincidunt, libero lorem convallis lectus <i class="fa fa-quote-right"></i> </blockquote>
                    </div>
                </div>
                <div class="testimonial">
                    <div class="author-img">
                        <img src="image/testimonials-client-2.png" alt="" class="img-fluid rounded">
                    </div>
                    <div class="author-quote">
                        <h4>Smith Pettle</h4>
                        <span>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip6">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip8">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip9">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip10">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip7">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>                                                                                       
                        </span>
                            <span>(5 Out of 5)</span>
                        <blockquote> <i class="fa fa-quote-left"></i> Maecenas varius finibus orci vel dignissim. Nam posuere, magna pellentesque accumsan tincidunt, libero lorem convallis lectus <i class="fa fa-quote-right"></i> </blockquote>
                    </div>
                </div>
                <div class="testimonial">
                    <div class="author-img">
                        <img src="image/testimonials-client-3.png" alt="" class="img-fluid rounded">
                    </div>
                    <div class="author-quote">
                        <h4>Alex Smith</h4>
                        <span>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip12">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip13">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0)">
                                    <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                    </g>
                                    <defs>
                                    <clipPath id="clip14">
                                    <rect width="15" height="15" fill="white"/>
                                    </clipPath>
                                    </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <g clip-path="url(#clip0)">
                                        <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                        </g>
                                        <defs>
                                        <clipPath id="clip15">
                                        <rect width="15" height="15" fill="white"/>
                                        </clipPath>
                                        </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <g clip-path="url(#clip0)">
                                            <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                            </g>
                                            <defs>
                                            <clipPath id="clip01">
                                            <rect width="15" height="15" fill="white"/>
                                            </clipPath>
                                            </defs>
                            </svg>                                                                                        
                        </span>
                            <span>(5 Out of 5)</span>
                        <blockquote> <i class="fa fa-quote-left"></i> Maecenas varius finibus orci vel dignissim. Nam posuere, magna pellentesque accumsan tincidunt, libero lorem convallis lectus <i class="fa fa-quote-right"></i> </blockquote>
                    </div>
                </div>
                <div class="testimonial">
                    <div class="author-img">
                        <img src="image/testimonials-client-4.png" alt="" class="img-fluid rounded">
                    </div>
                    <div class="author-quote">
                        <h4>Hasley Modricks</h4>
                        <span><svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0)">
                            <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                            </g>
                            <defs>
                            <clipPath id="clip02">
                            <rect width="15" height="15" fill="white"/>
                            </clipPath>
                            </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip03">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip04">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip05">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip06">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>                                                                                       
                            </span>
                            <span>(5 Out of 5)</span>
                        <blockquote> <i class="fa fa-quote-left"></i> Maecenas varius finibus orci vel dignissim. Nam posuere, magna pellentesque accumsan tincidunt, libero lorem convallis lectus <i class="fa fa-quote-right"></i> </blockquote>
                    </div>
                </div>
                <div class="testimonial">
                    <div class="author-img">
                        <img src="image/testimonials-client-5.png" alt="" class="img-fluid rounded">
                    </div>
                    <div class="author-quote">
                        <h4>Job Stivs</h4>
                        <span><svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0)">
                            <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                            </g>
                            <defs>
                            <clipPath id="clip07">
                            <rect width="15" height="15" fill="white"/>
                            </clipPath>
                            </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip08">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip09">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip010)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip020">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                <path d="M14.9609 5.75395C14.8627 5.45021 14.5933 5.23449 14.2746 5.20576L9.94517 4.81265L8.2332 0.805623C8.10697 0.51196 7.81949 0.321869 7.50008 0.321869C7.18066 0.321869 6.89318 0.51196 6.76695 0.80631L5.05498 4.81265L0.724885 5.20576C0.406731 5.23517 0.138017 5.45021 0.0392523 5.75395C-0.0595127 6.05768 0.031699 6.39083 0.272374 6.60083L3.5449 9.47086L2.57991 13.7216C2.50929 14.0342 2.6306 14.3573 2.88993 14.5447C3.02933 14.6454 3.19241 14.6967 3.35686 14.6967C3.49866 14.6967 3.63931 14.6585 3.76554 14.5829L7.50008 12.3509L11.2332 14.5829C11.5064 14.7473 11.8508 14.7323 12.1095 14.5447C12.369 14.3567 12.4902 14.0335 12.4196 13.7216L11.4546 9.47086L14.7271 6.6014C14.9678 6.39083 15.0597 6.05825 14.9609 5.75395V5.75395Z" fill="#FFC107"/>
                                </g>
                                <defs>
                                <clipPath id="clip040">
                                <rect width="15" height="15" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>                                                                                         
                            </span>
                            <span>(5 Out of 5)</span>
                        <blockquote> <i class="fa fa-quote-left"></i> Maecenas varius finibus orci vel dignissim. Nam posuere, magna pellentesque accumsan tincidunt, libero lorem convallis lectus <i class="fa fa-quote-right"></i> </blockquote>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!--testimonial end-->

    <!-- footer section -->

    <!-- Site footer -->
    
    <footer class="footer-section">
		<div class="container">
			<div class="footer-cta">
				<div class="row">
					<div class="col-xl-4 col-md-4 mb-30">
						<div class="single-cta">
							<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M30.7722 14.4613C30.7721 13.3197 30.4334 12.2038 29.7991 11.2546C29.1647 10.3055 28.2631 9.56578 27.2084 9.12901C26.1536 8.69224 24.9931 8.57804 23.8734 8.80085C22.7537 9.02366 21.7253 9.57348 20.9181 10.3808C20.1109 11.1881 19.5612 12.2166 19.3386 13.3363C19.1159 14.456 19.2303 15.6165 19.6672 16.6712C20.1041 17.7259 20.8439 18.6274 21.7932 19.2616C22.7424 19.8959 23.8584 20.2344 25 20.2344C26.5304 20.2325 27.9977 19.6237 29.0798 18.5414C30.1619 17.4591 30.7706 15.9918 30.7722 14.4613V14.4613ZM20.5949 14.4613C20.5951 13.5901 20.8535 12.7385 21.3377 12.0142C21.8218 11.2899 22.5098 10.7254 23.3148 10.3921C24.1197 10.0588 25.0054 9.97171 25.8599 10.1418C26.7143 10.3118 27.4991 10.7314 28.1151 11.3475C28.7311 11.9636 29.1506 12.7485 29.3205 13.603C29.4904 14.4575 29.4031 15.3432 29.0697 16.148C28.7362 16.9529 28.1716 17.6409 27.4472 18.1249C26.7228 18.6089 25.8712 18.8672 25 18.8672C23.832 18.8658 22.7122 18.4012 21.8863 17.5752C21.0605 16.7492 20.596 15.6294 20.5949 14.4613V14.4613Z" fill="black"/>
							<path d="M48.175 47.2926L42.6023 32.6832C42.5529 32.5539 42.4654 32.4426 42.3514 32.364C42.2373 32.2855 42.1021 32.2434 41.9637 32.2434H31.4168C31.4813 32.1383 31.5453 32.0363 31.6098 31.9309C35.668 25.2121 37.7102 19.3348 37.6809 14.4645C37.6814 11.1012 36.3459 7.87542 33.9681 5.49683C31.5903 3.11823 28.365 1.78163 25.0018 1.78106C21.6385 1.78049 18.4127 3.116 16.0341 5.49379C13.6555 7.87158 12.3189 11.0969 12.3184 14.4602C12.2891 19.3348 14.3312 25.2129 18.3895 31.9309C18.4539 32.0375 18.518 32.1394 18.5824 32.2434H8.03554C7.89708 32.2434 7.76189 32.2855 7.64785 32.364C7.53381 32.4426 7.4463 32.5539 7.39687 32.6832L1.825 47.2926C1.7853 47.3961 1.77139 47.5077 1.78449 47.6178C1.79759 47.7279 1.83729 47.8331 1.90017 47.9244C1.96305 48.0157 2.04722 48.0903 2.1454 48.1418C2.24357 48.1933 2.35281 48.2201 2.46367 48.2199H47.5363C47.6472 48.2201 47.7564 48.1933 47.8546 48.1418C47.9528 48.0903 48.0369 48.0157 48.0998 47.9244C48.1627 47.8331 48.2024 47.7279 48.2155 47.6178C48.2286 47.5077 48.2147 47.3961 48.175 47.2926V47.2926ZM13.6859 14.4613C13.6862 12.9755 13.9791 11.5044 14.5479 10.1318C15.1167 8.75919 15.9503 7.51208 17.0011 6.46165C18.0519 5.41122 19.2993 4.57805 20.6721 4.0097C22.0449 3.44136 23.5162 3.14896 25.002 3.14922C26.4877 3.14948 27.9589 3.44238 29.3315 4.0112C30.7041 4.58002 31.9512 5.41362 33.0016 6.46441C34.0521 7.5152 34.8852 8.7626 35.4536 10.1354C36.0219 11.5082 36.3143 12.9794 36.3141 14.4652C36.3727 24.1754 27.2945 36.1516 25 39.0234C22.7055 36.1527 13.627 24.1797 13.6859 14.4613V14.4613ZM24.4793 40.5469C24.5435 40.6223 24.6233 40.6829 24.7132 40.7245C24.8031 40.766 24.901 40.7876 25 40.7876C25.099 40.7876 25.1969 40.766 25.2868 40.7245C25.3767 40.6829 25.4565 40.6223 25.5207 40.5469C27.3357 38.3359 29.0183 36.0195 30.5598 33.6098H41.4922L42.3672 35.9047L21.0035 43.5027L13.3887 33.6086H19.4395C20.9811 36.0187 22.664 38.3355 24.4793 40.5469V40.5469ZM20.9797 44.9609H20.9836L33.7285 40.4297L38.6719 46.8527H15.6641L20.9797 44.9609ZM8.50703 33.6086H11.6633L19.6484 43.9844L11.5848 46.8523H3.45586L8.50703 33.6086ZM40.3988 46.8527L35.0828 39.9457L42.8562 37.1812L46.5449 46.8527H40.3988Z" fill="black"/>
							</svg>
							<div class="cta-text">
								<h4>Find Us</h4>
								<span>1010 Avenue, sw 54321, chandigarh</span>
							</div>
						</div>
					</div>
					<div class="col-xl-4 col-md-4 mb-30">
						<div class="single-cta">
							<svg width="51" height="51" viewBox="0 0 51 51" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M29.75 2.55005H21.2501C20.7809 2.55005 20.4001 2.93085 20.4001 3.40001C20.4001 3.86917 20.7809 4.24998 21.2501 4.24998H29.75C30.2192 4.24998 30.6 3.86917 30.6 3.40001C30.6 2.93085 30.2192 2.55005 29.75 2.55005Z" fill="black"/>
							<path d="M35.7 2.55005H34.8501C34.3809 2.55005 34.0001 2.93085 34.0001 3.40001C34.0001 3.86917 34.3809 4.24998 34.8501 4.24998H35.7C36.1692 4.24998 36.55 3.86917 36.55 3.40001C36.55 2.93085 36.1692 2.55005 35.7 2.55005Z" fill="black"/>
							<path d="M26.5557 43.35H24.4451C23.1523 43.35 22.1 44.4022 22.1 45.6951V46.1057C22.1 47.3985 23.1523 48.45 24.4443 48.45H26.5549C27.8477 48.45 28.9 47.3985 28.9 46.1057V45.6951C28.9 44.4022 27.8477 43.35 26.5557 43.35ZM27.2 46.1057C27.2 46.461 26.9111 46.7499 26.5558 46.7499H24.4451C24.089 46.7499 23.8 46.461 23.8 46.1057V45.6951C23.8 45.339 24.0889 45.0499 24.4451 45.0499H26.5549C26.9111 45.0499 27.2 45.3389 27.2 45.6951V46.1057V46.1057Z" fill="black"/>
							<path d="M36.8781 0H14.1227C12.4287 0 11.05 1.37869 11.05 3.07275V47.9273C11.05 49.6213 12.4287 51 14.1227 51H36.8772C38.5713 51 39.95 49.6213 39.95 47.9281V3.07275C39.9501 1.37869 38.5714 0 36.8781 0ZM38.25 47.9273C38.25 48.6838 37.6346 49.3 36.8781 49.3H14.1227C13.3654 49.3 12.75 48.6837 12.75 47.928V3.07275C12.75 2.31622 13.3654 1.70003 14.1227 1.70003H36.8772C37.6345 1.70003 38.2499 2.31632 38.2499 3.07275V47.9273H38.25Z" fill="black"/>
							<path d="M39.1 5.09998H11.9C11.4309 5.09998 11.0501 5.48078 11.0501 5.94994V41.6499C11.0501 42.1191 11.4309 42.4999 11.9 42.4999H39.1001C39.5692 42.4999 39.95 42.1191 39.95 41.6499V5.94994C39.95 5.48078 39.5692 5.09998 39.1 5.09998ZM38.25 40.8H12.75V6.80001H38.25V40.8Z" fill="black"/>
							</svg>

							<div class="cta-text">
								<h4>Call Us</h4>
								<span>+98765 432 100</span>
							</div>
						</div>
					</div>
					<div class="col-xl-4 col-md-4 mb-30">
						<div class="single-cta">
							<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M49.9903 19.1157C49.9851 19.0583 49.9737 19.0016 49.9561 18.9466C49.9447 18.8975 49.9288 18.8495 49.9086 18.8032C49.8853 18.758 49.8574 18.7152 49.8253 18.6757C49.7932 18.6287 49.756 18.5854 49.7145 18.5465C49.7011 18.5348 49.6953 18.5189 49.6812 18.5073L41.6666 12.2829V5.83176C41.6666 4.45103 40.5472 3.33163 39.1665 3.33163H30.1352L26.5191 0.523971C25.6258 -0.172744 24.3731 -0.172744 23.4798 0.523971L19.8629 3.33163H10.8316C9.45082 3.33163 8.33142 4.45093 8.33142 5.83176V12.283L0.319395 18.5074C0.305234 18.5191 0.299374 18.5349 0.286092 18.5466C0.244586 18.5854 0.207377 18.6288 0.175247 18.6758C0.143214 18.7153 0.115283 18.758 0.0919419 18.8033C0.071726 18.8495 0.0559049 18.8976 0.0444786 18.9467C0.0270949 19.0009 0.0156685 19.0567 0.0102971 19.1134C0.0102971 19.1309 0.000335693 19.1459 0.000335693 19.1634V47.5005C0.00150763 48.0306 0.173001 48.5462 0.489521 48.9714C0.494501 48.9789 0.49538 48.9881 0.501142 48.9947C0.507002 49.0013 0.519503 49.0089 0.527804 49.018C0.995992 49.6341 1.72415 49.9973 2.49792 50.0006H47.5002C48.277 49.9983 49.0082 49.6333 49.477 49.0139C49.4836 49.0056 49.4937 49.0031 49.4994 48.9948C49.5053 48.9865 49.5061 48.979 49.5112 48.9715C49.8277 48.5462 49.9992 48.0306 50.0003 47.5006V19.1657C50.0002 19.1483 49.9912 19.1332 49.9903 19.1157ZM24.4989 1.83898C24.79 1.60772 25.2021 1.60772 25.4931 1.83898L27.4157 3.33153H22.5821L24.4989 1.83898ZM2.7079 48.3339L24.499 31.4072C24.7903 31.1763 25.2021 31.1763 25.4932 31.4072L47.2901 48.3339H2.7079ZM48.3336 47.0346L26.5191 30.0921C25.6256 29.3959 24.3733 29.3959 23.4798 30.0921L1.66448 47.0346V20.4349L15.321 31.0397C15.6849 31.3218 16.2086 31.2556 16.4907 30.8917C16.7729 30.5278 16.7067 30.0042 16.3428 29.722L2.46872 18.949L8.33152 14.3929V20.8324C8.33152 21.2927 8.70469 21.6658 9.16486 21.6658C9.62514 21.6658 9.99821 21.2926 9.99821 20.8324V5.83166C9.99821 5.37138 10.3713 4.99832 10.8316 4.99832H39.1663C39.6266 4.99832 39.9996 5.37138 39.9996 5.83166V20.8324C39.9996 21.2927 40.3727 21.6658 40.833 21.6658C41.2933 21.6658 41.6663 21.2926 41.6663 20.8324V14.3929L47.5291 18.949L33.631 29.7413C33.3919 29.9224 33.2685 30.2182 33.3082 30.5155C33.3478 30.8129 33.5442 31.0661 33.8225 31.1783C34.1007 31.2905 34.4178 31.2446 34.6527 31.0579L48.3334 20.4349V47.0346H48.3336Z" fill="black"/>
							<path d="M34.9993 19.9981V16.6646C34.9993 11.1415 30.5219 6.66409 24.9987 6.66409C19.4756 6.66409 14.9982 11.1415 14.9982 16.6646C14.9982 22.1877 19.4756 26.6651 24.9987 26.6651C25.459 26.6651 25.8321 26.292 25.8321 25.8318C25.8321 25.3715 25.459 24.9984 24.9987 24.9984C20.3962 24.9984 16.665 21.2673 16.665 16.6647C16.665 12.0621 20.3962 8.33097 24.9987 8.33097C29.6013 8.33097 33.3325 12.0621 33.3325 16.6647V19.9982C33.3325 20.9187 32.5862 21.665 31.6657 21.665C30.7451 21.665 29.9989 20.9187 29.9989 19.9982V16.6647C29.9989 16.2044 29.6258 15.8314 29.1656 15.8314C28.7053 15.8314 28.3322 16.2044 28.3322 16.6647C28.3322 18.5057 26.8398 19.9982 24.9987 19.9982C23.1577 19.9982 21.6653 18.5057 21.6653 16.6647C21.6653 14.8237 23.1577 13.3312 24.9987 13.3312C25.459 13.3312 25.8321 12.9582 25.8321 12.4979C25.8321 12.0376 25.459 11.6645 24.9987 11.6645C22.7608 11.6596 20.7921 13.1422 20.1788 15.2944C19.5655 17.4467 20.4568 19.7443 22.3612 20.9198C24.2656 22.0952 26.719 21.8622 28.3681 20.3492C28.5495 22.1116 30.0813 23.422 31.8506 23.3283C33.6199 23.2346 35.0048 21.7698 34.9993 19.9981Z" fill="black"/>
							</svg>
							<div class="cta-text">
								<h4>Mail Us</h4>
								<span><EMAIL></span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="footer-content pt-5 pb-5">
				<div class="row">
					<div class="col-xl-4 col-lg-4 mb-50">
						<div class="footer-widget">
							<div class="footer-logo">
								<a href="index.html">
									<img src="image/logo-corp-king.png" alt="logo"></a>
							</div>
							<div class="footer-text">
								<p>Lorem ipsum dolor sit amet, consec tetur adipisicing elit, sed do eiusmod tempor incididuntut consec tetur adipisicing
								elit,Lorem ipsum dolor sit amet.</p>
							</div>
							<div class="footer-social-icon">
								<span>Follow Us</span>
								<a href="#">
								<svg width="20" height="20" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M25.6055 0H4.39453C1.97159 0 0 1.97159 0 4.39453V25.6055C0 28.0284 1.97159 30 4.39453 30H13.2422V19.3945H9.72656V14.1211H13.2422V10.5469C13.2422 7.63893 15.6077 5.27344 18.5156 5.27344H23.8477V10.5469H18.5156V14.1211H23.8477L22.9688 19.3945H18.5156V30H25.6055C28.0284 30 30 28.0284 30 25.6055V4.39453C30 1.97159 28.0284 0 25.6055 0Z" fill="#1CB5F1"/>
									</svg>
								</a>
								<a href="#"><svg width="20" height="20" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M27 0H3C1.35 0 0 1.35 0 3V27C0 28.65 1.35 30 3 30H27C28.65 30 30 28.65 30 27V3C30 1.35 28.65 0 27 0ZM23.55 10.95C23.4 17.85 19.05 22.65 12.45 22.95C9.75 23.1 7.8 22.2 6 21.15C7.95 21.4501 10.5 20.7001 11.85 19.5C9.9 19.35 8.7 18.3 8.1 16.65C8.7 16.8 9.3 16.65 9.75 16.65C7.95 16.05 6.75 15 6.6 12.6C7.05 12.9 7.65 13.05 8.25 13.05C6.9 12.3 6 9.45 7.05 7.65C9 9.75 11.4 11.55 15.3 11.85C14.25 7.65 19.9501 5.4 22.2001 8.25C23.2501 8.1 24.0001 7.65 24.7501 7.35C24.4501 8.4 23.8501 9 23.1001 9.6C23.8501 9.45 24.6001 9.3 25.2001 9C25.05 9.75 24.3 10.35 23.55 10.95Z" fill="#1AB2E1"/>
								</svg>
								</a>
								<a href="#"><svg width="20" height="20" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M13.8 10.35C13.8 8.85 12.9 5.85 10.65 5.85C9.75 5.85 8.7 6.45 8.7 8.4C8.7 10.2 9.6 12.75 11.7 12.75C11.7 12.75 13.8 12.6 13.8 10.35ZM12.9 17.7C12.75 17.7 12.6 17.7 12.45 17.7C12 17.7 10.65 17.85 9.75 18.1499C8.7 18.45 7.5 19.2 7.5 20.7C7.5 22.35 9 24 12 24C14.25 24 15.6 22.5 15.6 21C15.6 19.95 14.85 19.2 12.9 17.7ZM27 0H3C1.35 0 0 1.35 0 3V27C0 28.65 1.35 30 3 30H27C28.65 30 30 28.65 30 27V3C30 1.35 28.65 0 27 0ZM10.65 25.8C6.45 25.8 4.5 23.4 4.5 21.3C4.5 20.55 4.65 18.9 6.75 17.7001C7.95 16.9501 9.45 16.5001 11.4 16.3501C11.1 16.05 10.95 15.6001 10.95 14.8501C10.95 14.5501 10.95 14.4001 11.1 14.1001H10.5C7.5 14.1001 5.7 11.8501 5.7 9.60006C5.7 7.05006 7.65 4.20006 11.85 4.20006H18.15L17.7001 4.65006L16.65 5.7L16.5 5.85H15.45C16.0499 6.45 16.7999 7.5 16.7999 9.15C16.7999 11.25 15.7499 12.3 14.3999 13.2C14.0999 13.35 13.7999 13.8 13.7999 14.25C13.7999 14.7 14.0999 15 14.3999 15.15C14.5499 15.3 14.8499 15.4501 15.1499 15.6C16.3499 16.5 17.9999 17.5499 17.9999 19.95C18 22.65 16.05 25.8 10.65 25.8ZM25.5 15H22.5V18H21V15H18V13.5H21V10.5H22.5V13.5H25.5V15Z" fill="#1DB4EF"/>
								</svg>
								</a>
							</div>
						</div>
					</div>
					<div class="col-xl-4 col-lg-4 col-md-6 mb-30">
						<div class="footer-widget">
							<div class="footer-widget-heading">
								<h3>Links</h3>
							</div>
							<ul>
								<li><a href="index.html">Home</a></li>
								<li><a href="about.html">about</a></li>
								<li><a href="service.html">services</a></li>
								<li><a href="portfolio">portfolio</a></li>
								<li><a href="#vedio-demo">Our Apps</a></li>
								<li><a href="#corpo-features">Features</a></li>
								<li><a href="blog-details.html">Blog Details</a></li>
								<li><a href="team.html">Expert Team</a></li>
								<li><a href="contact.html">Contact us</a></li>
								<li><a href="blog.html">Latest News</a></li>
							</ul>
						</div>
					</div>
					<div class="col-xl-4 col-lg-4 col-md-6 mb-50">
						<div class="footer-widget">
							<div class="footer-widget-heading">
								<h3>Subscribe</h3>
							</div>
							<div class="footer-text mb-25">
								<p>Don’t miss to subscribe to our new feeds, kindly fill the form below.</p>
							</div>
							<div class="subscribe-form">
								<form action="#">
									<input type="text" placeholder="Email Address">
									<button class="subscribe"><i class="fa fa-paper-plane" aria-hidden="true"></i></button>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="copyright-area">
			<div class="container">
				<div class="row">
					<div class="col-xl-6 col-lg-6 text-center text-lg-left">
						<div class="copyright-text">
							<p>Copyright &copy; 2027, All Right Reserved. <a target="_blank" href="https://www.mobanwang.com/" title="网站模板">网站模板</a></p>
						</div>
					</div>
					<div class="col-xl-6 col-lg-6 d-none d-lg-block text-right">
						<div class="footer-menu">
							<ul>
								<li><a href="#">Home</a></li>
								<li><a href="#">Terms</a></li>
								<li><a href="#">Privacy</a></li>
								<li><a href="#">Policy</a></li>
								<li><a href="#">Contact</a></li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
		<a class="scrollToTop" href="slider-banner" style="">
			<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
				<g clip-path="url(#clip0)">
				<path d="M22.0312 10.5469C21.6131 10.5469 21.2177 10.6451 20.8661 10.8191C20.4299 9.96305 19.5404 9.375 18.5156 9.375C18.0975 9.375 17.7021 9.47326 17.3504 9.64723C16.9143 8.79117 16.0248 8.20312 15 8.20312C14.6919 8.20312 14.3962 8.25674 14.1211 8.3543V2.63672C14.1211 1.18283 12.9383 0 11.4844 0C10.0305 0 8.84766 1.18283 8.84766 2.63672V12.3171C6.88412 12.4673 5.33203 14.1118 5.33203 16.1133V20.3858C5.33203 21.7311 5.70586 23.0471 6.41314 24.1914C7.02305 25.1782 7.85783 26.0003 8.84766 26.5938V29.1211C8.84766 29.6065 9.24117 30 9.72656 30H20.2734C20.7588 30 21.1523 29.6065 21.1523 29.1211V26.5938C22.1422 26.0003 22.977 25.1783 23.5869 24.1914C24.2941 23.0471 24.668 21.7311 24.668 20.3858V13.1836C24.668 11.7297 23.4851 10.5469 22.0312 10.5469ZM22.9102 20.3858C22.9102 22.4752 21.7492 24.3537 19.8804 25.2881C19.5826 25.437 19.3945 25.7413 19.3945 26.0742V28.2422H10.6055V26.0742C10.6055 25.7413 10.4174 25.437 10.1196 25.2881C8.25076 24.3537 7.08984 22.4752 7.08984 20.3858V16.1133C7.08984 15.082 7.85514 14.226 8.84766 14.0834V16.1133C8.84766 16.5987 9.24117 16.9922 9.72656 16.9922C10.212 16.9922 10.6055 16.5987 10.6055 16.1133V2.63672C10.6055 2.15209 10.9997 1.75781 11.4844 1.75781C11.969 1.75781 12.3633 2.15209 12.3633 2.63672V16.1133C12.3633 16.5987 12.7568 16.9922 13.2422 16.9922C13.7276 16.9922 14.1211 16.5987 14.1211 16.1133V10.8398C14.1211 10.3552 14.5154 9.96094 15 9.96094C15.4846 9.96094 15.8789 10.3552 15.8789 10.8398V16.1133C15.8789 16.5987 16.2724 16.9922 16.7578 16.9922C17.2432 16.9922 17.6367 16.5987 17.6367 16.1133V12.0117C17.6367 11.5271 18.031 11.1328 18.5156 11.1328C19.0003 11.1328 19.3945 11.5271 19.3945 12.0117V16.1133C19.3945 16.5987 19.788 16.9922 20.2734 16.9922C20.7588 16.9922 21.1523 16.5987 21.1523 16.1133V13.1836C21.1523 12.699 21.5466 12.3047 22.0312 12.3047C22.5159 12.3047 22.9102 12.699 22.9102 13.1836V20.3858Z" fill="black"/>
				</g>
				<defs>
				<clipPath id="clip08">
				<rect width="30" height="30" fill="white"/>
				</clipPath>
				</defs>
			</svg>	
		</a>
	</footer>

    <!-- footer section end -->

        <!-- jquery-3.5.0.min - js -->
    <script src="js/jquery-3.5.0.min.js"></script>
    <!-- bootstrap.min - js -->
    <script src="js/bootstrap.min.js"></script>
    <script src="js/fontawesome.min.js"></script>
    <!-- popper.min - js -->
    <script src="js/popper.min.js"></script>
    <!-- owl.carousel.min - js -->
    <script src="js/owl.carousel.min.js"></script>
    <!-- wow.min - js -->   
    <script src="js/wow.min.js"></script>
    <!-- silk - js -->
    <script src="js/slick.min.js"></script>
    <!-- font awesome -->
    <script src="js/all.js"></script>
    <!-- main - js -->  
    <script src="js/main.js"></script> 
</body>
</html>